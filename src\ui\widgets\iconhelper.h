#ifndef ICONHELPER_H
#define ICONHELPER_H

#include <QString>
#include <QIcon>
#include <QPainter>
#include <QPixmap>
#include <QFont>
#include <QFontMetrics>
#include <QColor>

/**
 * @brief The IconHelper class provides utility functions for creating icons
 */
class IconHelper
{
public:
    /**
     * @brief Create an icon from Unicode text
     * @param unicode Unicode character or emoji
     * @param size Icon size
     * @param color Text color
     * @param backgroundColor Background color (transparent by default)
     * @return QIcon
     */
    static QIcon createTextIcon(const QString &unicode, int size = 24, 
                               const QColor &color = QColor(255, 255, 255),
                               const QColor &backgroundColor = QColor(0, 0, 0, 0));
    
    /**
     * @brief Create a colored circle icon
     * @param color Circle color
     * @param size Icon size
     * @return QIcon
     */
    static QIcon createCircleIcon(const QColor &color, int size = 24);
    
    /**
     * @brief Create a rounded rectangle icon
     * @param color Rectangle color
     * @param size Icon size
     * @return QIcon
     */
    static QIcon createRoundedRectIcon(const QColor &color, int size = 24);
    
    /**
     * @brief Get predefined icons for common actions
     */
    static QIcon getAddIcon();
    static QIcon getEditIcon();
    static QIcon getDeleteIcon();
    static QIcon getRefreshIcon();
    static QIcon getSearchIcon();
    static QIcon getViewIcon();
    static QIcon getSaveIcon();
    static QIcon getPrintIcon();
    static QIcon getDashboardIcon();
    static QIcon getInventoryIcon();
    static QIcon getSalesIcon();
    static QIcon getCustomersIcon();
    static QIcon getSuppliersIcon();
    static QIcon getReportsIcon();
};

#endif // ICONHELPER_H
