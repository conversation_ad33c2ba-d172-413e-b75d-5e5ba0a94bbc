#include "saleswidget.h"
#include "invoicedialog.h"

SalesWidget::SalesWidget(QWidget *parent)
    : QWidget(parent),
      m_salesService(this)
{
    createUI();
    loadInvoices();
}

void SalesWidget::loadInvoices()
{
    // Get invoices from service
    m_invoices = m_salesService.getAllSalesInvoices();

    // Update the model
    updateInvoiceModel();
}

void SalesWidget::createInvoice()
{
    InvoiceDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        QSharedPointer<Invoice> invoice = dialog.getInvoice();
        if (m_salesService.createSalesInvoice(invoice)) {
            QMessageBox::information(this, tr("Success"),
                                   tr("Invoice created successfully."));
            loadInvoices(); // Refresh the list
        } else {
            QMessageBox::critical(this, tr("Error"),
                                tr("Failed to create invoice."));
        }
    }
}

void SalesWidget::viewInvoice()
{
    // Get the selected invoice
    QModelIndex currentIndex = m_invoicesTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select an invoice to view."));
        return;
    }

    // Get the invoice ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int invoiceId = m_invoicesModel->data(m_invoicesModel->index(row, 0), Qt::UserRole).toInt();

    // Get the invoice from the service
    QSharedPointer<Invoice> invoice = m_salesService.getSalesInvoiceById(invoiceId);
    if (!invoice) {
        QMessageBox::warning(this, tr("Error"), tr("Invoice not found."));
        return;
    }

    // Show the invoice dialog in view/edit mode
    InvoiceDialog dialog(invoice, this);
    if (dialog.exec() == QDialog::Accepted) {
        QSharedPointer<Invoice> updatedInvoice = dialog.getInvoice();
        if (m_salesService.updateSalesInvoice(updatedInvoice)) {
            QMessageBox::information(this, tr("Success"),
                                   tr("Invoice updated successfully."));
            loadInvoices(); // Refresh the list
        } else {
            QMessageBox::critical(this, tr("Error"),
                                tr("Failed to update invoice."));
        }
    }
}

void SalesWidget::deleteInvoice()
{
    // Get the selected invoice
    QModelIndex currentIndex = m_invoicesTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select an invoice to delete."));
        return;
    }

    // Get the invoice ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int invoiceId = m_invoicesModel->data(m_invoicesModel->index(row, 0), Qt::UserRole).toInt();
    QString invoiceNumber = m_invoicesModel->data(m_invoicesModel->index(row, 1)).toString();

    // Confirm deletion
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, tr("Confirm Deletion"),
        tr("Are you sure you want to delete the invoice '%1'?").arg(invoiceNumber),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // Delete the invoice
        if (m_salesService.deleteSalesInvoice(invoiceId)) {
            loadInvoices();
        } else {
            QMessageBox::critical(this, tr("Error"), tr("Failed to delete the invoice."));
        }
    }
}

void SalesWidget::filterInvoices()
{
    // TODO: Implement invoice filtering
    QMessageBox::information(this, tr("Filter Invoices"),
                           tr("Invoice filtering not implemented yet."));
}

void SalesWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    m_titleLabel = new QLabel(tr("Sales Management"), this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(m_titleLabel);

    // Toolbar
    m_toolbarLayout = new QHBoxLayout();
    m_toolbarLayout->setSpacing(10);

    m_createButton = new QPushButton(" Create Invoice", this);
    m_createButton->setIcon(IconHelper::createTextIcon("📄", 16));
    m_createButton->setProperty("class", "primary");
    connect(m_createButton, &QPushButton::clicked, this, &SalesWidget::createInvoice);

    m_viewButton = new QPushButton(" View/Edit", this);
    m_viewButton->setIcon(IconHelper::getViewIcon());
    m_viewButton->setEnabled(false);
    connect(m_viewButton, &QPushButton::clicked, this, &SalesWidget::viewInvoice);

    m_deleteButton = new QPushButton(" Delete", this);
    m_deleteButton->setIcon(IconHelper::getDeleteIcon());
    m_deleteButton->setProperty("class", "danger");
    m_deleteButton->setEnabled(false);
    connect(m_deleteButton, &QPushButton::clicked, this, &SalesWidget::deleteInvoice);

    m_refreshButton = new QPushButton(" Refresh", this);
    m_refreshButton->setIcon(IconHelper::getRefreshIcon());
    connect(m_refreshButton, &QPushButton::clicked, this, &SalesWidget::loadInvoices);

    m_toolbarLayout->addWidget(m_createButton);
    m_toolbarLayout->addWidget(m_viewButton);
    m_toolbarLayout->addWidget(m_deleteButton);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_refreshButton);

    m_mainLayout->addLayout(m_toolbarLayout);

    // Search bar
    m_searchLayout = new QHBoxLayout();
    m_searchLayout->setSpacing(10);

    QLabel *searchLabel = new QLabel(tr("Search:"), this);
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Enter invoice number or customer name"));

    QLabel *statusLabel = new QLabel(tr("Status:"), this);
    m_statusCombo = new QComboBox(this);
    m_statusCombo->addItem(tr("All"));
    m_statusCombo->addItem(tr("Paid"));
    m_statusCombo->addItem(tr("Unpaid"));
    m_statusCombo->addItem(tr("Partial"));

    QLabel *dateRangeLabel = new QLabel(tr("Date Range:"), this);
    m_fromDateEdit = new QDateEdit(QDate::currentDate().addMonths(-1), this);
    m_fromDateEdit->setCalendarPopup(true);
    QLabel *toLabel = new QLabel(tr("to"), this);
    m_toDateEdit = new QDateEdit(QDate::currentDate(), this);
    m_toDateEdit->setCalendarPopup(true);

    m_searchButton = new QPushButton(tr("Search"), this);
    m_searchButton->setIcon(QIcon::fromTheme("system-search", QIcon(":/icons/search.png")));
    connect(m_searchButton, &QPushButton::clicked, this, &SalesWidget::filterInvoices);

    m_searchLayout->addWidget(searchLabel);
    m_searchLayout->addWidget(m_searchEdit, 1);
    m_searchLayout->addWidget(statusLabel);
    m_searchLayout->addWidget(m_statusCombo);
    m_searchLayout->addWidget(dateRangeLabel);
    m_searchLayout->addWidget(m_fromDateEdit);
    m_searchLayout->addWidget(toLabel);
    m_searchLayout->addWidget(m_toDateEdit);
    m_searchLayout->addWidget(m_searchButton);

    m_mainLayout->addLayout(m_searchLayout);

    // Invoices table
    m_invoicesTable = new QTableView(this);
    m_invoicesTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_invoicesTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_invoicesTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_invoicesTable->setAlternatingRowColors(true);
    m_invoicesTable->setSortingEnabled(true);
    m_invoicesTable->horizontalHeader()->setStretchLastSection(true);
    m_invoicesTable->verticalHeader()->setVisible(false);

    // Create the model
    m_invoicesModel = new QStandardItemModel(0, 7, this);
    m_invoicesModel->setHorizontalHeaderLabels({
        tr("ID"), tr("Invoice Number"), tr("Customer"), tr("Date"),
        tr("Total Amount"), tr("Paid Amount"), tr("Status")
    });

    // Create the proxy model for filtering and sorting
    m_proxyModel = new QSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_invoicesModel);
    m_proxyModel->setFilterKeyColumn(1); // Filter by invoice number
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);

    m_invoicesTable->setModel(m_proxyModel);

    // Hide the ID column
    m_invoicesTable->hideColumn(0);

    // Connect signals
    connect(m_invoicesTable->selectionModel(), &QItemSelectionModel::selectionChanged,
            [this]() {
                bool hasSelection = m_invoicesTable->selectionModel()->hasSelection();
                m_viewButton->setEnabled(hasSelection);
                m_deleteButton->setEnabled(hasSelection);
            });

    m_mainLayout->addWidget(m_invoicesTable);
}

void SalesWidget::updateInvoiceModel()
{
    // Clear the model
    m_invoicesModel->removeRows(0, m_invoicesModel->rowCount());

    // Add invoices to the model
    for (const QSharedPointer<Invoice> &invoice : m_invoices) {
        QList<QStandardItem*> row;

        // ID (hidden, used for reference)
        QStandardItem *idItem = new QStandardItem(QString::number(invoice->id()));
        idItem->setData(invoice->id(), Qt::UserRole);
        row.append(idItem);

        // Invoice Number
        row.append(new QStandardItem(invoice->invoiceNumber()));

        // Customer
        QSharedPointer<Customer> customer = m_salesService.getCustomerById(invoice->customerId());
        QString customerName = customer ? customer->name() : tr("Unknown");
        row.append(new QStandardItem(customerName));

        // Date
        row.append(new QStandardItem(invoice->createdAt().toString("yyyy-MM-dd")));

        // Total Amount
        row.append(new QStandardItem(QString::number(invoice->netAmount(), 'f', 2)));

        // Paid Amount
        row.append(new QStandardItem(QString::number(invoice->paidAmount(), 'f', 2)));

        // Status
        QStandardItem *statusItem = new QStandardItem(Invoice::statusToString(invoice->status()));
        if (invoice->status() == Invoice::Status::Paid) {
            statusItem->setForeground(Qt::green);
        } else if (invoice->status() == Invoice::Status::Unpaid) {
            statusItem->setForeground(Qt::red);
        } else {
            statusItem->setForeground(QColor(255, 165, 0)); // Orange
        }
        row.append(statusItem);

        m_invoicesModel->appendRow(row);
    }

    // Resize columns to contents
    m_invoicesTable->resizeColumnsToContents();
}
