#include "translationmanager.h"
#include "arabictranslations.h"

TranslationManager::TranslationManager(QObject *parent)
    : QObject(parent),
      m_app(nullptr),
      m_translator(nullptr),
      m_qtTranslator(nullptr),
      m_currentLanguage(English)
{
}

TranslationManager::~TranslationManager()
{
    if (m_translator) {
        if (m_app) {
            m_app->removeTranslator(m_translator);
        }
        delete m_translator;
    }

    if (m_qtTranslator) {
        if (m_app) {
            m_app->removeTranslator(m_qtTranslator);
        }
        delete m_qtTranslator;
    }
}

void TranslationManager::initialize(QApplication *app)
{
    m_app = app;

    // Create translators
    m_translator = new QTranslator(this);
    m_qtTranslator = new QTranslator(this);

    // Load language from settings
    loadLanguageFromSettings();
}

bool TranslationManager::setLanguage(Language language)
{
    if (m_currentLanguage == language) {
        return true;
    }

    // Remove existing translators
    if (m_translator && m_app) {
        m_app->removeTranslator(m_translator);
    }
    if (m_qtTranslator && m_app) {
        m_app->removeTranslator(m_qtTranslator);
    }

    // Load new translation
    if (loadTranslation(language)) {
        m_currentLanguage = language;

        // Setup RTL if Arabic
        setupRTL(language == Arabic);

        // Save to settings
        saveLanguageToSettings();

        // Emit signal
        emit languageChanged();

        return true;
    }

    return false;
}

TranslationManager::Language TranslationManager::currentLanguage() const
{
    return m_currentLanguage;
}

bool TranslationManager::isRTL() const
{
    return m_currentLanguage == Arabic;
}

QString TranslationManager::getLanguageName(Language language)
{
    switch (language) {
        case English:
            return "English";
        case Arabic:
            return "العربية";
        default:
            return "English";
    }
}

QList<TranslationManager::Language> TranslationManager::getAvailableLanguages()
{
    return {English, Arabic};
}

void TranslationManager::loadLanguageFromSettings()
{
    QSettings settings;
    QString languageCode = settings.value("language", "en").toString();

    Language language = English;
    if (languageCode == "ar") {
        language = Arabic;
    }

    setLanguage(language);
}

void TranslationManager::saveLanguageToSettings()
{
    QSettings settings;
    QString languageCode = (m_currentLanguage == Arabic) ? "ar" : "en";
    settings.setValue("language", languageCode);
}

bool TranslationManager::loadTranslation(Language language)
{
    if (!m_app) {
        return false;
    }

    QString languageCode;
    QString qtLanguageCode;

    switch (language) {
        case English:
            // English is the default, no translation needed
            return true;

        case Arabic:
            languageCode = "ar";
            qtLanguageCode = "ar";
            break;

        default:
            return false;
    }

    // Load application translation
    QString translationPath = ":/translations/accountant_" + languageCode + ".qm";
    if (m_translator->load(translationPath)) {
        m_app->installTranslator(m_translator);
    } else {
        qDebug() << "Failed to load translation:" << translationPath;
        // Continue anyway, some translations might be missing
    }

    // Load Qt translation
    QString qtTranslationPath = ":/translations/qt_" + qtLanguageCode + ".qm";
    if (m_qtTranslator->load(qtTranslationPath)) {
        m_app->installTranslator(m_qtTranslator);
    } else {
        qDebug() << "Failed to load Qt translation:" << qtTranslationPath;
        // This is optional, continue anyway
    }

    return true;
}

void TranslationManager::setupRTL(bool enable)
{
    if (!m_app) {
        return;
    }

    if (enable) {
        // Set RTL layout direction
        m_app->setLayoutDirection(Qt::RightToLeft);

        // Set Arabic locale
        QLocale::setDefault(QLocale(QLocale::Arabic, QLocale::SaudiArabia));
    } else {
        // Set LTR layout direction
        m_app->setLayoutDirection(Qt::LeftToRight);

        // Set English locale
        QLocale::setDefault(QLocale(QLocale::English, QLocale::UnitedStates));
    }
}
