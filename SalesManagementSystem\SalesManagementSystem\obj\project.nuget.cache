{"version": 2, "dgSpecHash": "GlFTceneGf0=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\accountant\\SalesManagementSystem\\SalesManagementSystem\\SalesManagementSystem.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\controlzex\\4.4.0\\controlzex.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapper\\2.0.123\\dapper.2.0.123.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\entityframework\\6.4.4\\entityframework.6.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.2.2\\fluentvalidation.11.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\7.3.0\\harfbuzzsharp.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\7.3.0\\harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\7.3.0\\harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\livechartscore\\2.0.0-rc2\\livechartscore.2.0.0-rc2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\livechartscore.skiasharpview\\2.0.0-rc2\\livechartscore.skiasharpview.2.0.0-rc2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\livechartscore.skiasharpview.wpf\\2.0.0-rc2\\livechartscore.skiasharpview.wpf.2.0.0-rc2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mahapps.metro\\2.4.9\\mahapps.metro.2.4.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\2.0.9\\materialdesigncolors.2.0.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\4.6.1\\materialdesignthemes.4.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.19\\microsoft.xaml.behaviors.wpf.1.1.19.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlog\\5.0.4\\nlog.5.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.core\\8.1.97\\prism.core.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.6\\skiasharp.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.harfbuzz\\2.88.6\\skiasharp.harfbuzz.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.6\\skiasharp.nativeassets.macos.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.6\\skiasharp.nativeassets.win32.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.desktop.common\\2.88.6\\skiasharp.views.desktop.common.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.wpf\\2.88.6\\skiasharp.views.wpf.2.88.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stub.system.data.sqlite.core.netstandard\\1.0.116\\stub.system.data.sqlite.core.netstandard.1.0.116.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.1\\system.data.sqlclient.4.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite\\1.0.116\\system.data.sqlite.1.0.116.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.core\\1.0.116\\system.data.sqlite.core.1.0.116.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlite.ef6\\1.0.116\\system.data.sqlite.ef6.1.0.116.nupkg.sha512"], "logs": []}