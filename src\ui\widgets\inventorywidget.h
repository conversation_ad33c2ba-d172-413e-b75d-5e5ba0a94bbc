#ifndef INVENTORYWIDGET_H
#define INVENTORYWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QTableView>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QSortFilterProxyModel>
#include <QMessageBox>
#include <QDialog>
#include <QFormLayout>
#include <QDoubleSpinBox>
#include <QSpinBox>
#include <QDialogButtonBox>
#include <QDebug>

#include "../../core/services/inventoryservice.h"
#include "../../core/models/product.h"
#include "iconhelper.h"

/**
 * @brief The InventoryWidget class displays and manages inventory
 */
class InventoryWidget : public QWidget
{
    Q_OBJECT

public:
    explicit InventoryWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Load products from the database
     */
    void loadProducts();

    /**
     * @brief Add a new product
     */
    void addProduct();

    /**
     * @brief Edit the selected product
     */
    void editProduct();

    /**
     * @brief Delete the selected product
     */
    void deleteProduct();

    /**
     * @brief Filter products based on search text
     */
    void filterProducts();

    /**
     * @brief Handle double-click on a product to edit it
     * @param index The model index that was double-clicked
     */
    void onProductDoubleClicked(const QModelIndex &index);

    /**
     * @brief Update the UI when a product is selected
     */
    void onProductSelectionChanged();

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create the product dialog for adding/editing
     * @param product Product to edit, or null for a new product
     * @return true if the dialog was accepted, false otherwise
     */
    bool showProductDialog(QSharedPointer<Product> product = nullptr);

    /**
     * @brief Update the product model with the current products
     */
    void updateProductModel();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolbarLayout;
    QHBoxLayout *m_searchLayout;

    // Toolbar widgets
    QLabel *m_titleLabel;
    QPushButton *m_addButton;
    QPushButton *m_editButton;
    QPushButton *m_deleteButton;
    QPushButton *m_refreshButton;

    // Search widgets
    QLineEdit *m_searchEdit;
    QComboBox *m_categoryCombo;
    QPushButton *m_searchButton;

    // Table view
    QTableView *m_productsTable;
    QStandardItemModel *m_productsModel;
    QSortFilterProxyModel *m_proxyModel;

    // Service
    InventoryService m_inventoryService;

    // Current products
    QList<QSharedPointer<Product>> m_products;
};

#endif // INVENTORYWIDGET_H
