#ifndef SUPPLIER_H
#define SUPPLIER_H

#include <QString>
#include <QDateTime>

/**
 * @brief The Supplier class represents a supplier in the system
 */
class Supplier
{
public:
    Supplier();
    Supplier(int id, const QString &name, const QString &phone, const QString &email,
             const QString &address, double balance);
    
    // Getters
    int id() const;
    QString name() const;
    QString phone() const;
    QString email() const;
    QString address() const;
    double balance() const;
    QDateTime createdAt() const;
    QDateTime updatedAt() const;
    
    // Setters
    void setId(int id);
    void setName(const QString &name);
    void setPhone(const QString &phone);
    void setEmail(const QString &email);
    void setAddress(const QString &address);
    void setBalance(double balance);
    void setCreatedAt(const QDateTime &dateTime);
    void setUpdatedAt(const QDateTime &dateTime);
    
    // Business logic
    bool hasCredit() const;
    
private:
    int m_id;
    QString m_name;
    QString m_phone;
    QString m_email;
    QString m_address;
    double m_balance;
    QDateTime m_createdAt;
    QDateTime m_updatedAt;
};

#endif // SUPPLIER_H
