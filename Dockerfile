FROM ubuntu:24.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Update package list and install basic tools
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    software-properties-common \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Qt5 instead of Qt6 for better compatibility
RUN apt-get update && apt-get install -y \
    qtbase5-dev \
    qtdeclarative5-dev \
    libqt5charts5-dev \
    libqt5sql5-sqlite \
    libqt5core5a \
    libqt5gui5 \
    libqt5widgets5 \
    libqt5charts5 \
    libqt5sql5 \
    libgl1-mesa-dev \
    x11-apps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy the source code
COPY . .

# Create build directory
RUN mkdir -p build

# Build the application
WORKDIR /app/build
RUN cmake .. && make

# Create database directory
RUN mkdir -p /app/database

# Set the entry point
CMD ["/app/build/AccountantApp"]
