#include "databasemanager.h"

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent)
{
    // Set the database path
    m_dbPath = QDir::currentPath() + "/database/accountant.db";
}

DatabaseManager::~DatabaseManager()
{
    // Close the database connection if it's open
    if (m_db.isOpen()) {
        m_db.close();
    }
}

bool DatabaseManager::initialize()
{
    // Create the database directory if it doesn't exist
    QDir dbDir(QDir::currentPath() + "/database");
    if (!dbDir.exists()) {
        if (!dbDir.mkpath(".")) {
            qDebug() << "Failed to create database directory";
            return false;
        }
    }
    
    // Initialize the database connection
    m_db = QSqlDatabase::addDatabase("QSQLITE");
    m_db.setDatabaseName(m_dbPath);
    
    // Open the database
    if (!m_db.open()) {
        qDebug() << "Failed to open database:" << m_db.lastError().text();
        return false;
    }
    
    // Create tables if they don't exist
    if (!createTables()) {
        qDebug() << "Failed to create database tables";
        return false;
    }
    
    qDebug() << "Database initialized successfully";
    return true;
}

QSqlDatabase DatabaseManager::database() const
{
    return m_db;
}

bool DatabaseManager::executeQuery(const QString &query)
{
    QSqlQuery sqlQuery;
    if (!sqlQuery.exec(query)) {
        qDebug() << "Query failed:" << sqlQuery.lastError().text();
        qDebug() << "Query was:" << query;
        return false;
    }
    return true;
}

bool DatabaseManager::executePreparedQuery(QSqlQuery &query, const QVariantList &bindValues)
{
    // Bind values to the query
    for (const QVariant &value : bindValues) {
        query.addBindValue(value);
    }
    
    // Execute the query
    if (!query.exec()) {
        qDebug() << "Prepared query failed:" << query.lastError().text();
        qDebug() << "Query was:" << query.lastQuery();
        return false;
    }
    return true;
}

bool DatabaseManager::createTables()
{
    // Create all required tables
    if (!createProductsTable()) return false;
    if (!createCustomersTable()) return false;
    if (!createSuppliersTable()) return false;
    if (!createInvoicesTable()) return false;
    if (!createInvoiceItemsTable()) return false;
    if (!createPaymentsTable()) return false;
    
    return true;
}

bool DatabaseManager::createProductsTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS products ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "code TEXT UNIQUE, "
                    "name TEXT NOT NULL, "
                    "description TEXT, "
                    "category TEXT, "
                    "purchase_price REAL, "
                    "selling_price REAL, "
                    "quantity INTEGER DEFAULT 0, "
                    "min_quantity INTEGER DEFAULT 0, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                    ")";
    
    return executeQuery(query);
}

bool DatabaseManager::createCustomersTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS customers ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "name TEXT NOT NULL, "
                    "phone TEXT, "
                    "email TEXT, "
                    "address TEXT, "
                    "balance REAL DEFAULT 0, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                    ")";
    
    return executeQuery(query);
}

bool DatabaseManager::createSuppliersTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS suppliers ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "name TEXT NOT NULL, "
                    "phone TEXT, "
                    "email TEXT, "
                    "address TEXT, "
                    "balance REAL DEFAULT 0, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                    ")";
    
    return executeQuery(query);
}

bool DatabaseManager::createInvoicesTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS invoices ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "invoice_number TEXT UNIQUE, "
                    "type TEXT NOT NULL, " // 'sale' or 'purchase'
                    "customer_id INTEGER, "
                    "supplier_id INTEGER, "
                    "total_amount REAL, "
                    "discount REAL DEFAULT 0, "
                    "tax REAL DEFAULT 0, "
                    "paid_amount REAL DEFAULT 0, "
                    "status TEXT, " // 'paid', 'unpaid', 'partial'
                    "notes TEXT, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "FOREIGN KEY (customer_id) REFERENCES customers (id), "
                    "FOREIGN KEY (supplier_id) REFERENCES suppliers (id)"
                    ")";
    
    return executeQuery(query);
}

bool DatabaseManager::createInvoiceItemsTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS invoice_items ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "invoice_id INTEGER, "
                    "product_id INTEGER, "
                    "quantity INTEGER, "
                    "unit_price REAL, "
                    "total_price REAL, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "FOREIGN KEY (invoice_id) REFERENCES invoices (id), "
                    "FOREIGN KEY (product_id) REFERENCES products (id)"
                    ")";
    
    return executeQuery(query);
}

bool DatabaseManager::createPaymentsTable()
{
    QString query = "CREATE TABLE IF NOT EXISTS payments ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "invoice_id INTEGER, "
                    "amount REAL, "
                    "payment_method TEXT, "
                    "reference TEXT, "
                    "notes TEXT, "
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, "
                    "FOREIGN KEY (invoice_id) REFERENCES invoices (id)"
                    ")";
    
    return executeQuery(query);
}
