#ifndef CUSTOMERSWIDGET_H
#define CUSTOMERSWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTableView>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QSortFilterProxyModel>
#include <QMessageBox>
#include <QDialog>
#include <QFormLayout>
#include <QDoubleSpinBox>
#include <QDialogButtonBox>
#include <QDebug>

#include "../../core/services/salesservice.h"
#include "../../core/models/customer.h"

/**
 * @brief The CustomersWidget class displays and manages customers
 */
class CustomersWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CustomersWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Load customers from the database
     */
    void loadCustomers();

    /**
     * @brief Add a new customer
     */
    void addCustomer();

    /**
     * @brief Edit the selected customer
     */
    void editCustomer();

    /**
     * @brief Delete the selected customer
     */
    void deleteCustomer();

    /**
     * @brief Filter customers based on search text
     */
    void filterCustomers();

    /**
     * @brief Handle double-click on a customer to edit it
     * @param index The model index that was double-clicked
     */
    void onCustomerDoubleClicked(const QModelIndex &index);

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create the customer dialog for adding/editing
     * @param customer Customer to edit, or null for a new customer
     * @return true if the dialog was accepted, false otherwise
     */
    bool showCustomerDialog(QSharedPointer<Customer> customer = nullptr);

    /**
     * @brief Update the customer model with the current customers
     */
    void updateCustomerModel();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolbarLayout;
    QHBoxLayout *m_searchLayout;

    // Toolbar widgets
    QLabel *m_titleLabel;
    QPushButton *m_addButton;
    QPushButton *m_editButton;
    QPushButton *m_deleteButton;
    QPushButton *m_refreshButton;

    // Search widgets
    QLineEdit *m_searchEdit;
    QPushButton *m_searchButton;

    // Table view
    QTableView *m_customersTable;
    QStandardItemModel *m_customersModel;
    QSortFilterProxyModel *m_proxyModel;

    // Service
    SalesService m_salesService;

    // Current customers
    QList<QSharedPointer<Customer>> m_customers;
};

#endif // CUSTOMERSWIDGET_H
