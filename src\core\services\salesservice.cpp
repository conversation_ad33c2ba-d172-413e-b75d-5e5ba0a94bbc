#include "salesservice.h"
#include <QDateTime>

SalesService::SalesService(QObject *parent)
    : QObject(parent),
      m_inventoryService(this)
{
    // Initialize database
    m_dbManager.initialize();
}

QList<QSharedPointer<Invoice>> SalesService::getAllSalesInvoices()
{
    QList<QSharedPointer<Invoice>> invoices;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE type = 'sale' ORDER BY created_at DESC");
    
    if (query.exec()) {
        while (query.next()) {
            QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
            loadInvoiceItems(invoice);
            invoices.append(invoice);
        }
    } else {
        qDebug() << "Failed to get sales invoices:" << query.lastError().text();
    }
    
    return invoices;
}

QSharedPointer<Invoice> SalesService::getSalesInvoiceById(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE id = ? AND type = 'sale'");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
        loadInvoiceItems(invoice);
        return invoice;
    } else {
        qDebug() << "Failed to get sales invoice by ID:" << query.lastError().text();
        return nullptr;
    }
}

QSharedPointer<Invoice> SalesService::getSalesInvoiceByNumber(const QString &invoiceNumber)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE invoice_number = ? AND type = 'sale'");
    query.addBindValue(invoiceNumber);
    
    if (query.exec() && query.next()) {
        QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
        loadInvoiceItems(invoice);
        return invoice;
    } else {
        qDebug() << "Failed to get sales invoice by number:" << query.lastError().text();
        return nullptr;
    }
}

bool SalesService::createSalesInvoice(const QSharedPointer<Invoice> &invoice)
{
    // Start a transaction
    QSqlDatabase db = m_dbManager.database();
    db.transaction();
    
    // Set invoice type to Sale
    invoice->setType(Invoice::Type::Sale);
    
    // Generate invoice number if not set
    if (invoice->invoiceNumber().isEmpty()) {
        invoice->setInvoiceNumber(generateInvoiceNumber());
    }
    
    // Insert the invoice
    QSqlQuery query(db);
    query.prepare("INSERT INTO invoices (invoice_number, type, customer_id, total_amount, "
                  "discount, tax, paid_amount, status, notes) "
                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    query.addBindValue(invoice->invoiceNumber());
    query.addBindValue(Invoice::typeToString(invoice->type()));
    query.addBindValue(invoice->customerId());
    query.addBindValue(invoice->totalAmount());
    query.addBindValue(invoice->discount());
    query.addBindValue(invoice->tax());
    query.addBindValue(invoice->paidAmount());
    query.addBindValue(Invoice::statusToString(invoice->status()));
    query.addBindValue(invoice->notes());
    
    if (!query.exec()) {
        qDebug() << "Failed to create sales invoice:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Set the ID of the newly added invoice
    invoice->setId(query.lastInsertId().toInt());
    
    // Save invoice items
    if (!saveInvoiceItems(invoice)) {
        db.rollback();
        return false;
    }
    
    // Update inventory quantities
    if (!updateInventory(invoice, true)) {
        db.rollback();
        return false;
    }
    
    // Update customer balance if not fully paid
    if (invoice->remainingAmount() > 0) {
        if (!updateCustomerBalance(invoice->customerId(), invoice->remainingAmount())) {
            db.rollback();
            return false;
        }
    }
    
    // Commit the transaction
    return db.commit();
}

bool SalesService::updateSalesInvoice(const QSharedPointer<Invoice> &invoice)
{
    // Get the original invoice to calculate balance changes
    QSharedPointer<Invoice> originalInvoice = getSalesInvoiceById(invoice->id());
    if (!originalInvoice) {
        return false;
    }
    
    // Start a transaction
    QSqlDatabase db = m_dbManager.database();
    db.transaction();
    
    // Update the invoice
    QSqlQuery query(db);
    query.prepare("UPDATE invoices SET customer_id = ?, total_amount = ?, "
                  "discount = ?, tax = ?, paid_amount = ?, status = ?, notes = ? "
                  "WHERE id = ? AND type = 'sale'");
    
    query.addBindValue(invoice->customerId());
    query.addBindValue(invoice->totalAmount());
    query.addBindValue(invoice->discount());
    query.addBindValue(invoice->tax());
    query.addBindValue(invoice->paidAmount());
    query.addBindValue(Invoice::statusToString(invoice->status()));
    query.addBindValue(invoice->notes());
    query.addBindValue(invoice->id());
    
    if (!query.exec()) {
        qDebug() << "Failed to update sales invoice:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Delete existing invoice items
    query.prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
    query.addBindValue(invoice->id());
    
    if (!query.exec()) {
        qDebug() << "Failed to delete invoice items:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Restore inventory quantities
    if (!updateInventory(originalInvoice, false)) {
        db.rollback();
        return false;
    }
    
    // Save new invoice items
    if (!saveInvoiceItems(invoice)) {
        db.rollback();
        return false;
    }
    
    // Update inventory with new quantities
    if (!updateInventory(invoice, true)) {
        db.rollback();
        return false;
    }
    
    // Update customer balance
    double balanceChange = invoice->remainingAmount() - originalInvoice->remainingAmount();
    if (balanceChange != 0) {
        if (!updateCustomerBalance(invoice->customerId(), balanceChange)) {
            db.rollback();
            return false;
        }
    }
    
    // Commit the transaction
    return db.commit();
}

bool SalesService::deleteSalesInvoice(int id)
{
    // Get the invoice to restore inventory quantities
    QSharedPointer<Invoice> invoice = getSalesInvoiceById(id);
    if (!invoice) {
        return false;
    }
    
    // Start a transaction
    QSqlDatabase db = m_dbManager.database();
    db.transaction();
    
    // Delete invoice items
    QSqlQuery query(db);
    query.prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
    query.addBindValue(id);
    
    if (!query.exec()) {
        qDebug() << "Failed to delete invoice items:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Delete payments
    query.prepare("DELETE FROM payments WHERE invoice_id = ?");
    query.addBindValue(id);
    
    if (!query.exec()) {
        qDebug() << "Failed to delete payments:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Restore inventory quantities
    if (!updateInventory(invoice, false)) {
        db.rollback();
        return false;
    }
    
    // Update customer balance if not fully paid
    if (invoice->remainingAmount() > 0) {
        if (!updateCustomerBalance(invoice->customerId(), -invoice->remainingAmount())) {
            db.rollback();
            return false;
        }
    }
    
    // Delete the invoice
    query.prepare("DELETE FROM invoices WHERE id = ? AND type = 'sale'");
    query.addBindValue(id);
    
    if (!query.exec()) {
        qDebug() << "Failed to delete sales invoice:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Commit the transaction
    return db.commit();
}

bool SalesService::addPayment(int invoiceId, double amount, const QString &paymentMethod,
                              const QString &reference, const QString &notes)
{
    // Get the invoice
    QSharedPointer<Invoice> invoice = getSalesInvoiceById(invoiceId);
    if (!invoice) {
        return false;
    }
    
    // Start a transaction
    QSqlDatabase db = m_dbManager.database();
    db.transaction();
    
    // Insert the payment
    QSqlQuery query(db);
    query.prepare("INSERT INTO payments (invoice_id, amount, payment_method, reference, notes) "
                  "VALUES (?, ?, ?, ?, ?)");
    
    query.addBindValue(invoiceId);
    query.addBindValue(amount);
    query.addBindValue(paymentMethod);
    query.addBindValue(reference);
    query.addBindValue(notes);
    
    if (!query.exec()) {
        qDebug() << "Failed to add payment:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Update the invoice paid amount and status
    double newPaidAmount = invoice->paidAmount() + amount;
    invoice->setPaidAmount(newPaidAmount);
    invoice->updateStatus();
    
    query.prepare("UPDATE invoices SET paid_amount = ?, status = ? WHERE id = ?");
    query.addBindValue(newPaidAmount);
    query.addBindValue(Invoice::statusToString(invoice->status()));
    query.addBindValue(invoiceId);
    
    if (!query.exec()) {
        qDebug() << "Failed to update invoice paid amount:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Update customer balance
    if (!updateCustomerBalance(invoice->customerId(), -amount)) {
        db.rollback();
        return false;
    }
    
    // Commit the transaction
    return db.commit();
}

QList<QSharedPointer<Customer>> SalesService::getAllCustomers()
{
    QList<QSharedPointer<Customer>> customers;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM customers ORDER BY name");
    
    if (query.exec()) {
        while (query.next()) {
            customers.append(createCustomerFromQuery(query));
        }
    } else {
        qDebug() << "Failed to get customers:" << query.lastError().text();
    }
    
    return customers;
}

QSharedPointer<Customer> SalesService::getCustomerById(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM customers WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        return createCustomerFromQuery(query);
    } else {
        qDebug() << "Failed to get customer by ID:" << query.lastError().text();
        return nullptr;
    }
}

bool SalesService::addCustomer(const QSharedPointer<Customer> &customer)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("INSERT INTO customers (name, phone, email, address, balance) "
                  "VALUES (?, ?, ?, ?, ?)");
    
    query.addBindValue(customer->name());
    query.addBindValue(customer->phone());
    query.addBindValue(customer->email());
    query.addBindValue(customer->address());
    query.addBindValue(customer->balance());
    
    if (query.exec()) {
        // Set the ID of the newly added customer
        customer->setId(query.lastInsertId().toInt());
        return true;
    } else {
        qDebug() << "Failed to add customer:" << query.lastError().text();
        return false;
    }
}

bool SalesService::updateCustomer(const QSharedPointer<Customer> &customer)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE customers SET name = ?, phone = ?, email = ?, address = ?, "
                  "balance = ?, updated_at = CURRENT_TIMESTAMP "
                  "WHERE id = ?");
    
    query.addBindValue(customer->name());
    query.addBindValue(customer->phone());
    query.addBindValue(customer->email());
    query.addBindValue(customer->address());
    query.addBindValue(customer->balance());
    query.addBindValue(customer->id());
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update customer:" << query.lastError().text();
        return false;
    }
}

bool SalesService::deleteCustomer(int id)
{
    // Check if customer has invoices
    QSqlQuery checkQuery(m_dbManager.database());
    checkQuery.prepare("SELECT COUNT(*) FROM invoices WHERE customer_id = ?");
    checkQuery.addBindValue(id);
    
    if (checkQuery.exec() && checkQuery.next()) {
        int count = checkQuery.value(0).toInt();
        if (count > 0) {
            qDebug() << "Cannot delete customer with invoices";
            return false;
        }
    } else {
        qDebug() << "Failed to check customer invoices:" << checkQuery.lastError().text();
        return false;
    }
    
    // Delete the customer
    QSqlQuery query(m_dbManager.database());
    query.prepare("DELETE FROM customers WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to delete customer:" << query.lastError().text();
        return false;
    }
}

QList<QSharedPointer<Customer>> SalesService::searchCustomers(const QString &searchTerm)
{
    QList<QSharedPointer<Customer>> customers;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM customers WHERE name LIKE ? OR phone LIKE ? ORDER BY name");
    query.addBindValue("%" + searchTerm + "%");
    query.addBindValue("%" + searchTerm + "%");
    
    if (query.exec()) {
        while (query.next()) {
            customers.append(createCustomerFromQuery(query));
        }
    } else {
        qDebug() << "Failed to search customers:" << query.lastError().text();
    }
    
    return customers;
}

QString SalesService::generateInvoiceNumber()
{
    // Get the current date
    QDate currentDate = QDate::currentDate();
    QString yearMonth = currentDate.toString("yyyyMM");
    
    // Get the last invoice number for this month
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT MAX(invoice_number) FROM invoices WHERE invoice_number LIKE ?");
    query.addBindValue("INV-" + yearMonth + "-%");
    
    int lastNumber = 0;
    if (query.exec() && query.next()) {
        QString lastInvoiceNumber = query.value(0).toString();
        if (!lastInvoiceNumber.isEmpty()) {
            QStringList parts = lastInvoiceNumber.split("-");
            if (parts.size() == 3) {
                lastNumber = parts[2].toInt();
            }
        }
    }
    
    // Generate the new invoice number
    return QString("INV-%1-%2").arg(yearMonth).arg(lastNumber + 1, 4, 10, QChar('0'));
}

QSharedPointer<Invoice> SalesService::createInvoiceFromQuery(QSqlQuery &query)
{
    QSharedPointer<Invoice> invoice = QSharedPointer<Invoice>(new Invoice());
    
    invoice->setId(query.value("id").toInt());
    invoice->setInvoiceNumber(query.value("invoice_number").toString());
    invoice->setType(Invoice::typeFromString(query.value("type").toString()));
    invoice->setCustomerId(query.value("customer_id").toInt());
    invoice->setSupplierId(query.value("supplier_id").toInt());
    invoice->setTotalAmount(query.value("total_amount").toDouble());
    invoice->setDiscount(query.value("discount").toDouble());
    invoice->setTax(query.value("tax").toDouble());
    invoice->setPaidAmount(query.value("paid_amount").toDouble());
    invoice->setStatus(Invoice::statusFromString(query.value("status").toString()));
    invoice->setNotes(query.value("notes").toString());
    invoice->setCreatedAt(query.value("created_at").toDateTime());
    
    return invoice;
}

QSharedPointer<Customer> SalesService::createCustomerFromQuery(QSqlQuery &query)
{
    QSharedPointer<Customer> customer = QSharedPointer<Customer>(new Customer());
    
    customer->setId(query.value("id").toInt());
    customer->setName(query.value("name").toString());
    customer->setPhone(query.value("phone").toString());
    customer->setEmail(query.value("email").toString());
    customer->setAddress(query.value("address").toString());
    customer->setBalance(query.value("balance").toDouble());
    customer->setCreatedAt(query.value("created_at").toDateTime());
    customer->setUpdatedAt(query.value("updated_at").toDateTime());
    
    return customer;
}

bool SalesService::loadInvoiceItems(const QSharedPointer<Invoice> &invoice)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    query.addBindValue(invoice->id());
    
    if (query.exec()) {
        QList<QSharedPointer<InvoiceItem>> items;
        while (query.next()) {
            QSharedPointer<InvoiceItem> item = QSharedPointer<InvoiceItem>(new InvoiceItem());
            
            item->setId(query.value("id").toInt());
            item->setInvoiceId(query.value("invoice_id").toInt());
            item->setProductId(query.value("product_id").toInt());
            item->setQuantity(query.value("quantity").toInt());
            item->setUnitPrice(query.value("unit_price").toDouble());
            item->setTotalPrice(query.value("total_price").toDouble());
            item->setCreatedAt(query.value("created_at").toDateTime());
            
            items.append(item);
        }
        invoice->setItems(items);
        return true;
    } else {
        qDebug() << "Failed to load invoice items:" << query.lastError().text();
        return false;
    }
}

bool SalesService::saveInvoiceItems(const QSharedPointer<Invoice> &invoice)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) "
                  "VALUES (?, ?, ?, ?, ?)");
    
    for (const QSharedPointer<InvoiceItem> &item : invoice->items()) {
        query.addBindValue(invoice->id());
        query.addBindValue(item->productId());
        query.addBindValue(item->quantity());
        query.addBindValue(item->unitPrice());
        query.addBindValue(item->totalPrice());
        
        if (!query.exec()) {
            qDebug() << "Failed to save invoice item:" << query.lastError().text();
            return false;
        }
        
        // Set the ID of the newly added item
        item->setId(query.lastInsertId().toInt());
    }
    
    return true;
}

bool SalesService::updateCustomerBalance(int customerId, double amount)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE customers SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP "
                  "WHERE id = ?");
    query.addBindValue(amount);
    query.addBindValue(customerId);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update customer balance:" << query.lastError().text();
        return false;
    }
}

bool SalesService::updateInventory(const QSharedPointer<Invoice> &invoice, bool isCreating)
{
    for (const QSharedPointer<InvoiceItem> &item : invoice->items()) {
        QSharedPointer<Product> product = m_inventoryService.getProductById(item->productId());
        if (!product) {
            qDebug() << "Product not found:" << item->productId();
            return false;
        }
        
        int newQuantity;
        if (isCreating) {
            // Decrease quantity for sales
            newQuantity = product->quantity() - item->quantity();
        } else {
            // Increase quantity when deleting sales
            newQuantity = product->quantity() + item->quantity();
        }
        
        if (!m_inventoryService.updateProductQuantity(product->id(), newQuantity)) {
            return false;
        }
    }
    
    return true;
}
