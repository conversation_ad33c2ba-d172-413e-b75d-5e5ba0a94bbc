#include "invoice.h"

// Invoice implementation
Invoice::Invoice()
    : m_id(0),
      m_type(Type::Sale),
      m_customerId(0),
      m_supplierId(0),
      m_totalAmount(0.0),
      m_discount(0.0),
      m_tax(0.0),
      m_paidAmount(0.0),
      m_status(Status::Unpaid)
{
}

Invoice::Invoice(int id, const QString &invoiceNumber, Type type, int customerId, int supplierId,
                 double totalAmount, double discount, double tax, double paidAmount,
                 Status status, const QString &notes)
    : m_id(id),
      m_invoiceNumber(invoiceNumber),
      m_type(type),
      m_customerId(customerId),
      m_supplierId(supplierId),
      m_totalAmount(totalAmount),
      m_discount(discount),
      m_tax(tax),
      m_paidAmount(paidAmount),
      m_status(status),
      m_notes(notes)
{
}

// Getters
int Invoice::id() const { return m_id; }
QString Invoice::invoiceNumber() const { return m_invoiceNumber; }
Invoice::Type Invoice::type() const { return m_type; }
int Invoice::customerId() const { return m_customerId; }
int Invoice::supplierId() const { return m_supplierId; }
double Invoice::totalAmount() const { return m_totalAmount; }
double Invoice::discount() const { return m_discount; }
double Invoice::tax() const { return m_tax; }
double Invoice::paidAmount() const { return m_paidAmount; }
Invoice::Status Invoice::status() const { return m_status; }
QString Invoice::notes() const { return m_notes; }
QDateTime Invoice::createdAt() const { return m_createdAt; }
QList<QSharedPointer<InvoiceItem>> Invoice::items() const { return m_items; }

// Setters
void Invoice::setId(int id) { m_id = id; }
void Invoice::setInvoiceNumber(const QString &invoiceNumber) { m_invoiceNumber = invoiceNumber; }
void Invoice::setType(Type type) { m_type = type; }
void Invoice::setCustomerId(int customerId) { m_customerId = customerId; }
void Invoice::setSupplierId(int supplierId) { m_supplierId = supplierId; }
void Invoice::setTotalAmount(double totalAmount) { m_totalAmount = totalAmount; }
void Invoice::setDiscount(double discount) { m_discount = discount; }
void Invoice::setTax(double tax) { m_tax = tax; }
void Invoice::setPaidAmount(double paidAmount) { m_paidAmount = paidAmount; updateStatus(); }
void Invoice::setStatus(Status status) { m_status = status; }
void Invoice::setNotes(const QString &notes) { m_notes = notes; }
void Invoice::setCreatedAt(const QDateTime &dateTime) { m_createdAt = dateTime; }
void Invoice::setItems(const QList<QSharedPointer<InvoiceItem>> &items) { m_items = items; }

// Business logic
double Invoice::netAmount() const
{
    return m_totalAmount - m_discount + m_tax;
}

double Invoice::remainingAmount() const
{
    return netAmount() - m_paidAmount;
}

void Invoice::addItem(const QSharedPointer<InvoiceItem> &item)
{
    m_items.append(item);
    m_totalAmount += item->totalPrice();
    updateStatus();
}

void Invoice::removeItem(int itemId)
{
    for (int i = 0; i < m_items.size(); ++i) {
        if (m_items[i]->id() == itemId) {
            m_totalAmount -= m_items[i]->totalPrice();
            m_items.removeAt(i);
            updateStatus();
            break;
        }
    }
}

void Invoice::updateStatus()
{
    double net = netAmount();
    if (m_paidAmount >= net) {
        m_status = Status::Paid;
    } else if (m_paidAmount > 0) {
        m_status = Status::Partial;
    } else {
        m_status = Status::Unpaid;
    }
}

// Static helpers
QString Invoice::typeToString(Type type)
{
    switch (type) {
    case Type::Sale:
        return "sale";
    case Type::Purchase:
        return "purchase";
    default:
        return "unknown";
    }
}

Invoice::Type Invoice::typeFromString(const QString &typeStr)
{
    if (typeStr.toLower() == "sale") {
        return Type::Sale;
    } else if (typeStr.toLower() == "purchase") {
        return Type::Purchase;
    } else {
        return Type::Sale; // Default
    }
}

QString Invoice::statusToString(Status status)
{
    switch (status) {
    case Status::Paid:
        return "paid";
    case Status::Unpaid:
        return "unpaid";
    case Status::Partial:
        return "partial";
    default:
        return "unknown";
    }
}

Invoice::Status Invoice::statusFromString(const QString &statusStr)
{
    if (statusStr.toLower() == "paid") {
        return Status::Paid;
    } else if (statusStr.toLower() == "unpaid") {
        return Status::Unpaid;
    } else if (statusStr.toLower() == "partial") {
        return Status::Partial;
    } else {
        return Status::Unpaid; // Default
    }
}

// InvoiceItem implementation
InvoiceItem::InvoiceItem()
    : m_id(0),
      m_invoiceId(0),
      m_productId(0),
      m_quantity(0),
      m_unitPrice(0.0),
      m_totalPrice(0.0)
{
}

InvoiceItem::InvoiceItem(int id, int invoiceId, int productId, int quantity, double unitPrice, double totalPrice)
    : m_id(id),
      m_invoiceId(invoiceId),
      m_productId(productId),
      m_quantity(quantity),
      m_unitPrice(unitPrice),
      m_totalPrice(totalPrice)
{
}

// Getters
int InvoiceItem::id() const { return m_id; }
int InvoiceItem::invoiceId() const { return m_invoiceId; }
int InvoiceItem::productId() const { return m_productId; }
int InvoiceItem::quantity() const { return m_quantity; }
double InvoiceItem::unitPrice() const { return m_unitPrice; }
double InvoiceItem::totalPrice() const { return m_totalPrice; }
QDateTime InvoiceItem::createdAt() const { return m_createdAt; }

// Setters
void InvoiceItem::setId(int id) { m_id = id; }
void InvoiceItem::setInvoiceId(int invoiceId) { m_invoiceId = invoiceId; }
void InvoiceItem::setProductId(int productId) { m_productId = productId; }
void InvoiceItem::setQuantity(int quantity) { m_quantity = quantity; updateTotalPrice(); }
void InvoiceItem::setUnitPrice(double unitPrice) { m_unitPrice = unitPrice; updateTotalPrice(); }
void InvoiceItem::setTotalPrice(double totalPrice) { m_totalPrice = totalPrice; }
void InvoiceItem::setCreatedAt(const QDateTime &dateTime) { m_createdAt = dateTime; }

// Business logic
void InvoiceItem::updateTotalPrice()
{
    m_totalPrice = m_quantity * m_unitPrice;
}
