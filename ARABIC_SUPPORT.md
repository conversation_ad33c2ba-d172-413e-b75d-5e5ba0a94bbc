# 🌍 دعم اللغة العربية - Arabic Language Support

## ✨ الميزات الجديدة

### 🔤 دعم اللغة العربية الكامل
- **واجهة عربية**: جميع النصوص مترجمة للعربية
- **اتجاه النص**: دعم RTL (من اليمين إلى اليسار)
- **الخطوط العربية**: دعم الخطوط العربية الحديثة
- **التبديل السريع**: تغيير اللغة من القائمة مباشرة

### 🎯 الترجمات المتاحة
- **الواجهة الرئيسية**: جميع القوائم والأزرار
- **لوحة التحكم**: البطاقات والإحصائيات
- **إدارة المخزون**: جداول المنتجات والبحث
- **إدارة المبيعات**: الفواتير والعمليات
- **إدارة العملاء**: معلومات العملاء
- **إدارة الموردين**: بيانات الموردين
- **التقارير**: جميع أنواع التقارير

## 🚀 كيفية الاستخدام

### تغيير اللغة
1. **من القائمة**: View → Language → العربية
2. **أو**: عرض → اللغة → العربية
3. **التبديل السريع**: Ctrl+L (قريباً)

### الميزات المدعومة
- ✅ **تبديل فوري**: تغيير اللغة بدون إعادة تشغيل
- ✅ **حفظ الإعدادات**: تذكر اللغة المختارة
- ✅ **اتجاه النص**: تلقائياً RTL للعربية
- ✅ **الخطوط**: دعم الخطوط العربية الحديثة

## 🔧 التفاصيل التقنية

### نظام الترجمة
```cpp
// استخدام الترجمات المباشرة
ArabicTranslations::translate("Dashboard") // → "لوحة التحكم"
ArabicTranslations::translate("Sales")     // → "المبيعات"
```

### إدارة الاتجاه
```cpp
// تلقائياً عند تغيير اللغة
app->setLayoutDirection(Qt::RightToLeft);  // للعربية
app->setLayoutDirection(Qt::LeftToRight);  // للإنجليزية
```

### الخطوط المدعومة
- **Segoe UI**: للنصوص العامة
- **Tahoma**: للنصوص العربية
- **Arial Unicode MS**: احتياطي
- **System Default**: خط النظام

## 📝 الترجمات المتاحة

### القوائم الرئيسية
| English | العربية |
|---------|---------|
| File | ملف |
| View | عرض |
| Tools | أدوات |
| Help | مساعدة |
| Language | اللغة |

### الصفحات
| English | العربية |
|---------|---------|
| Dashboard | لوحة التحكم |
| Inventory | المخزون |
| Sales | المبيعات |
| Customers | العملاء |
| Suppliers | الموردين |
| Reports | التقارير |

### العمليات
| English | العربية |
|---------|---------|
| Add Product | إضافة منتج |
| Edit | تعديل |
| Delete | حذف |
| Search | بحث |
| Refresh | تحديث |
| Save | حفظ |
| Cancel | إلغاء |

### البيانات
| English | العربية |
|---------|---------|
| Name | الاسم |
| Code | الكود |
| Category | الفئة |
| Price | السعر |
| Quantity | الكمية |
| Date | التاريخ |
| Status | الحالة |

## 🎨 التصميم العربي

### اتجاه النص
- **RTL Layout**: تخطيط من اليمين لليسار
- **Text Alignment**: محاذاة النص للجهة الصحيحة
- **Menu Direction**: قوائم تفتح بالاتجاه الصحيح

### الخطوط والألوان
- **خطوط واضحة**: خطوط عربية حديثة
- **ألوان متناسقة**: نفس نظام الألوان
- **حجم مناسب**: أحجام خطوط مريحة للقراءة

## 🔄 إضافة ترجمات جديدة

### للمطورين
```cpp
// في arabictranslations.cpp
m_translations["New Text"] = "النص الجديد";
m_translations["Another Text"] = "نص آخر";
```

### للمترجمين
1. افتح ملف `src/core/utils/arabictranslations.cpp`
2. أضف الترجمة الجديدة في دالة `initialize()`
3. احفظ الملف وأعد البناء

## 🐛 حل المشاكل

### النص لا يظهر بالعربية
- تأكد من اختيار العربية من القائمة
- أعد تشغيل البرنامج إذا لزم الأمر
- تحقق من دعم النظام للخطوط العربية

### الاتجاه خاطئ
- تأكد من تفعيل RTL في الإعدادات
- أعد تشغيل البرنامج
- تحقق من إعدادات النظام

### الخطوط غير واضحة
- تأكد من تثبيت خطوط عربية حديثة
- جرب تغيير حجم الخط من إعدادات النظام
- استخدم دقة شاشة مناسبة

## 📱 الدعم المستقبلي

### قريباً
- 🔄 **ترجمة الرسائل**: رسائل الخطأ والتأكيد
- 📊 **ترجمة التقارير**: محتوى التقارير المولدة
- 🎨 **ثيمات عربية**: تصاميم مخصصة للعربية
- ⌨️ **اختصارات لوحة المفاتيح**: اختصارات عربية

### مخطط له
- 🌍 **لغات إضافية**: فرنسية، ألمانية، إسبانية
- 📝 **ترجمة البيانات**: أسماء الفئات والحالات
- 🔍 **بحث عربي**: بحث محسن للنصوص العربية
- 📄 **طباعة عربية**: تقارير وفواتير بالعربية

## 💡 نصائح للاستخدام

### للمستخدمين العرب
1. **اختر العربية** من القائمة عند أول تشغيل
2. **استخدم البحث** بالعربية في جميع الصفحات
3. **أدخل البيانات** بالعربية أو الإنجليزية
4. **اطبع التقارير** بالاتجاه الصحيح

### للمستخدمين متعددي اللغات
1. **بدل اللغة** حسب الحاجة
2. **استخدم اختصارات** لوحة المفاتيح
3. **احفظ الإعدادات** لكل مستخدم
4. **شارك الملفات** بأي لغة

## 🎯 الخلاصة

تم إضافة دعم شامل للغة العربية يشمل:
- ✅ ترجمة كاملة للواجهة
- ✅ دعم اتجاه RTL
- ✅ خطوط عربية حديثة
- ✅ تبديل سريع بين اللغات
- ✅ حفظ الإعدادات تلقائياً

البرنامج الآن جاهز للاستخدام باللغة العربية بشكل كامل! 🎉
