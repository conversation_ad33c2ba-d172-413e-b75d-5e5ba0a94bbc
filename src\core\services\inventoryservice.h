#ifndef INVENTORYSERVICE_H
#define INVENTORYSERVICE_H

#include <QObject>
#include <QList>
#include <QSharedPointer>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include "../models/product.h"
#include "../database/databasemanager.h"

/**
 * @brief The InventoryService class handles inventory operations
 */
class InventoryService : public QObject
{
    Q_OBJECT
    
public:
    explicit InventoryService(QObject *parent = nullptr);
    
    /**
     * @brief Get all products
     * @return List of products
     */
    QList<QSharedPointer<Product>> getAllProducts();
    
    /**
     * @brief Get product by ID
     * @param id Product ID
     * @return Product pointer or null if not found
     */
    QSharedPointer<Product> getProductById(int id);
    
    /**
     * @brief Get product by code
     * @param code Product code
     * @return Product pointer or null if not found
     */
    QSharedPointer<Product> getProductByCode(const QString &code);
    
    /**
     * @brief Add a new product
     * @param product Product to add
     * @return true if successful, false otherwise
     */
    bool addProduct(const QSharedPointer<Product> &product);
    
    /**
     * @brief Update an existing product
     * @param product Product to update
     * @return true if successful, false otherwise
     */
    bool updateProduct(const QSharedPointer<Product> &product);
    
    /**
     * @brief Delete a product
     * @param id Product ID
     * @return true if successful, false otherwise
     */
    bool deleteProduct(int id);
    
    /**
     * @brief Update product quantity
     * @param id Product ID
     * @param quantity New quantity
     * @return true if successful, false otherwise
     */
    bool updateProductQuantity(int id, int quantity);
    
    /**
     * @brief Get low stock products
     * @return List of products with low stock
     */
    QList<QSharedPointer<Product>> getLowStockProducts();
    
    /**
     * @brief Search products by name or code
     * @param searchTerm Search term
     * @return List of matching products
     */
    QList<QSharedPointer<Product>> searchProducts(const QString &searchTerm);
    
private:
    /**
     * @brief Create a Product object from a SQL query
     * @param query SQL query with product data
     * @return Product pointer
     */
    QSharedPointer<Product> createProductFromQuery(QSqlQuery &query);
    
    DatabaseManager m_dbManager;
};

#endif // INVENTORYSERVICE_H
