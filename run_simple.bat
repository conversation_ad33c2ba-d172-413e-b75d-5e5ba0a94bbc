@echo off
echo ========================================
echo    Modern Accountant App - Quick Start
echo ========================================
echo.
echo Make sure VcXsrv is running with:
echo - Multiple windows
echo - Start no client  
echo - Disable access control (IMPORTANT!)
echo.
echo Building and starting the application...
echo This may take a few minutes on first run.
echo.

REM Create database directory
if not exist database mkdir database

REM Build and run with simplified output
docker-compose -f docker-compose.windows.yml up --build --remove-orphans

echo.
echo Application stopped.
pause
