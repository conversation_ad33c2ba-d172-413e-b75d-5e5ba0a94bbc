#include "customer.h"

Customer::Customer()
    : m_id(0),
      m_balance(0.0)
{
}

Customer::Customer(int id, const QString &name, const QString &phone, const QString &email,
                   const QString &address, double balance)
    : m_id(id),
      m_name(name),
      m_phone(phone),
      m_email(email),
      m_address(address),
      m_balance(balance)
{
}

// Getters
int Customer::id() const { return m_id; }
QString Customer::name() const { return m_name; }
QString Customer::phone() const { return m_phone; }
QString Customer::email() const { return m_email; }
QString Customer::address() const { return m_address; }
double Customer::balance() const { return m_balance; }
QDateTime Customer::createdAt() const { return m_createdAt; }
QDateTime Customer::updatedAt() const { return m_updatedAt; }

// Setters
void Customer::setId(int id) { m_id = id; }
void Customer::setName(const QString &name) { m_name = name; }
void Customer::setPhone(const QString &phone) { m_phone = phone; }
void Customer::setEmail(const QString &email) { m_email = email; }
void Customer::setAddress(const QString &address) { m_address = address; }
void Customer::setBalance(double balance) { m_balance = balance; }
void Customer::setCreatedAt(const QDateTime &dateTime) { m_createdAt = dateTime; }
void Customer::setUpdatedAt(const QDateTime &dateTime) { m_updatedAt = dateTime; }

// Business logic
bool Customer::hasDebt() const
{
    return m_balance > 0;
}
