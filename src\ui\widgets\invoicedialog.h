#ifndef INVOICEDIALOG_H
#define INVOICEDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include <QDateEdit>
#include <QTableWidget>
#include <QHeaderView>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QTextEdit>
#include <QDialogButtonBox>
#include <QMessageBox>
#include <QCompleter>
#include <QStringListModel>
#include <QGroupBox>
#include <QDebug>

#include "../../core/models/invoice.h"
#include "../../core/models/customer.h"
#include "../../core/models/product.h"
#include "../../core/services/salesservice.h"
#include "../../core/services/inventoryservice.h"

/**
 * @brief The InvoiceDialog class provides a dialog for creating/editing invoices
 */
class InvoiceDialog : public QDialog
{
    Q_OBJECT

public:
    explicit InvoiceDialog(QWidget *parent = nullptr);
    explicit InvoiceDialog(QSharedPointer<Invoice> invoice, QWidget *parent = nullptr);

    /**
     * @brief Get the invoice from the dialog
     * @return The invoice with all data filled
     */
    QSharedPointer<Invoice> getInvoice() const;

private slots:
    /**
     * @brief Handle customer selection change
     */
    void onCustomerChanged();

    /**
     * @brief Add a new item to the invoice
     */
    void addItem();

    /**
     * @brief Remove selected item from the invoice
     */
    void removeItem();

    /**
     * @brief Handle item changes in the table
     */
    void onItemChanged(int row, int column);

    /**
     * @brief Calculate and update totals
     */
    void updateTotals();

    /**
     * @brief Validate the invoice data
     */
    bool validateInvoice();

    /**
     * @brief Accept the dialog and save invoice
     */
    void accept() override;

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Load customers into the combo box
     */
    void loadCustomers();

    /**
     * @brief Load products for the completer
     */
    void loadProducts();

    /**
     * @brief Setup the items table
     */
    void setupItemsTable();

    /**
     * @brief Load invoice data into the form (for editing)
     */
    void loadInvoiceData();

    // UI Components
    QVBoxLayout *m_mainLayout;
    QFormLayout *m_headerLayout;
    QHBoxLayout *m_buttonLayout;

    // Header fields
    QLineEdit *m_invoiceNumberEdit;
    QComboBox *m_customerCombo;
    QDateEdit *m_dateEdit;
    QComboBox *m_statusCombo;

    // Items table
    QTableWidget *m_itemsTable;
    QPushButton *m_addItemButton;
    QPushButton *m_removeItemButton;

    // Totals
    QDoubleSpinBox *m_subtotalSpin;
    QDoubleSpinBox *m_discountSpin;
    QDoubleSpinBox *m_taxSpin;
    QDoubleSpinBox *m_totalSpin;
    QDoubleSpinBox *m_paidAmountSpin;
    QDoubleSpinBox *m_remainingSpin;

    // Notes
    QTextEdit *m_notesEdit;

    // Buttons
    QDialogButtonBox *m_buttonBox;

    // Data
    QSharedPointer<Invoice> m_invoice;
    QList<QSharedPointer<Customer>> m_customers;
    QList<QSharedPointer<Product>> m_products;
    QStringList m_productNames;

    // Services
    SalesService m_salesService;
    InventoryService m_inventoryService;

    // Editing mode
    bool m_isEditing;
};

#endif // INVOICEDIALOG_H
