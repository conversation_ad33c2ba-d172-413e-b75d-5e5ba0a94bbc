#ifndef PURCHASESERVICE_H
#define PURCHASESERVICE_H

#include <QObject>
#include <QList>
#include <QSharedPointer>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include "../models/invoice.h"
#include "../models/supplier.h"
#include "../database/databasemanager.h"
#include "inventoryservice.h"

/**
 * @brief The PurchaseService class handles purchase operations
 */
class PurchaseService : public QObject
{
    Q_OBJECT
    
public:
    explicit PurchaseService(QObject *parent = nullptr);
    
    /**
     * @brief Get all purchase invoices
     * @return List of purchase invoices
     */
    QList<QSharedPointer<Invoice>> getAllPurchaseInvoices();
    
    /**
     * @brief Get purchase invoice by ID
     * @param id Invoice ID
     * @return Invoice pointer or null if not found
     */
    QSharedPointer<Invoice> getPurchaseInvoiceById(int id);
    
    /**
     * @brief Get purchase invoice by invoice number
     * @param invoiceNumber Invoice number
     * @return Invoice pointer or null if not found
     */
    QSharedPointer<Invoice> getPurchaseInvoiceByNumber(const QString &invoiceNumber);
    
    /**
     * @brief Create a new purchase invoice
     * @param invoice Invoice to create
     * @return true if successful, false otherwise
     */
    bool createPurchaseInvoice(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Update an existing purchase invoice
     * @param invoice Invoice to update
     * @return true if successful, false otherwise
     */
    bool updatePurchaseInvoice(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Delete a purchase invoice
     * @param id Invoice ID
     * @return true if successful, false otherwise
     */
    bool deletePurchaseInvoice(int id);
    
    /**
     * @brief Add payment to a purchase invoice
     * @param invoiceId Invoice ID
     * @param amount Payment amount
     * @param paymentMethod Payment method
     * @param reference Payment reference
     * @param notes Payment notes
     * @return true if successful, false otherwise
     */
    bool addPayment(int invoiceId, double amount, const QString &paymentMethod,
                    const QString &reference, const QString &notes);
    
    /**
     * @brief Get all suppliers
     * @return List of suppliers
     */
    QList<QSharedPointer<Supplier>> getAllSuppliers();
    
    /**
     * @brief Get supplier by ID
     * @param id Supplier ID
     * @return Supplier pointer or null if not found
     */
    QSharedPointer<Supplier> getSupplierById(int id);
    
    /**
     * @brief Add a new supplier
     * @param supplier Supplier to add
     * @return true if successful, false otherwise
     */
    bool addSupplier(const QSharedPointer<Supplier> &supplier);
    
    /**
     * @brief Update an existing supplier
     * @param supplier Supplier to update
     * @return true if successful, false otherwise
     */
    bool updateSupplier(const QSharedPointer<Supplier> &supplier);
    
    /**
     * @brief Delete a supplier
     * @param id Supplier ID
     * @return true if successful, false otherwise
     */
    bool deleteSupplier(int id);
    
    /**
     * @brief Search suppliers by name or phone
     * @param searchTerm Search term
     * @return List of matching suppliers
     */
    QList<QSharedPointer<Supplier>> searchSuppliers(const QString &searchTerm);
    
    /**
     * @brief Generate a new invoice number
     * @return New invoice number
     */
    QString generateInvoiceNumber();
    
private:
    /**
     * @brief Create an Invoice object from a SQL query
     * @param query SQL query with invoice data
     * @return Invoice pointer
     */
    QSharedPointer<Invoice> createInvoiceFromQuery(QSqlQuery &query);
    
    /**
     * @brief Create a Supplier object from a SQL query
     * @param query SQL query with supplier data
     * @return Supplier pointer
     */
    QSharedPointer<Supplier> createSupplierFromQuery(QSqlQuery &query);
    
    /**
     * @brief Load invoice items for an invoice
     * @param invoice Invoice to load items for
     * @return true if successful, false otherwise
     */
    bool loadInvoiceItems(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Save invoice items for an invoice
     * @param invoice Invoice to save items for
     * @return true if successful, false otherwise
     */
    bool saveInvoiceItems(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Update supplier balance
     * @param supplierId Supplier ID
     * @param amount Amount to add to balance (negative to subtract)
     * @return true if successful, false otherwise
     */
    bool updateSupplierBalance(int supplierId, double amount);
    
    /**
     * @brief Update inventory quantities based on invoice items
     * @param invoice Invoice with items
     * @param isCreating Whether the invoice is being created (true) or deleted (false)
     * @return true if successful, false otherwise
     */
    bool updateInventory(const QSharedPointer<Invoice> &invoice, bool isCreating);
    
    DatabaseManager m_dbManager;
    InventoryService m_inventoryService;
};

#endif // PURCHASESERVICE_H
