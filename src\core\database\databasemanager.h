#ifndef DATABASEMANAGER_H
#define DATABASEMANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QString>
#include <QDebug>
#include <QDir>

/**
 * @brief The DatabaseManager class handles database connections and operations
 */
class DatabaseManager : public QObject
{
    Q_OBJECT
    
public:
    explicit DatabaseManager(QObject *parent = nullptr);
    ~DatabaseManager();
    
    /**
     * @brief Initialize the database connection and create tables if needed
     * @return true if successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Get the database connection
     * @return QSqlDatabase instance
     */
    QSqlDatabase database() const;
    
    /**
     * @brief Execute a SQL query
     * @param query The SQL query to execute
     * @return true if successful, false otherwise
     */
    bool executeQuery(const QString &query);
    
    /**
     * @brief Execute a prepared SQL query with parameters
     * @param query The prepared SQL query
     * @param bindValues The values to bind to the query
     * @return true if successful, false otherwise
     */
    bool executePreparedQuery(QSqlQuery &query, const QVariantList &bindValues);
    
private:
    /**
     * @brief Create database tables if they don't exist
     * @return true if successful, false otherwise
     */
    bool createTables();
    
    /**
     * @brief Create the products table
     * @return true if successful, false otherwise
     */
    bool createProductsTable();
    
    /**
     * @brief Create the customers table
     * @return true if successful, false otherwise
     */
    bool createCustomersTable();
    
    /**
     * @brief Create the suppliers table
     * @return true if successful, false otherwise
     */
    bool createSuppliersTable();
    
    /**
     * @brief Create the invoices table
     * @return true if successful, false otherwise
     */
    bool createInvoicesTable();
    
    /**
     * @brief Create the invoice items table
     * @return true if successful, false otherwise
     */
    bool createInvoiceItemsTable();
    
    /**
     * @brief Create the payments table
     * @return true if successful, false otherwise
     */
    bool createPaymentsTable();
    
    QSqlDatabase m_db; // Database connection
    QString m_dbPath;  // Path to the database file
};

#endif // DATABASEMANAGER_H
