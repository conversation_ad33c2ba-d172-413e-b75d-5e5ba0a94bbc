#include "supplier.h"

Supplier::Supplier()
    : m_id(0),
      m_balance(0.0)
{
}

Supplier::Supplier(int id, const QString &name, const QString &phone, const QString &email,
                   const QString &address, double balance)
    : m_id(id),
      m_name(name),
      m_phone(phone),
      m_email(email),
      m_address(address),
      m_balance(balance)
{
}

// Getters
int Supplier::id() const { return m_id; }
QString Supplier::name() const { return m_name; }
QString Supplier::phone() const { return m_phone; }
QString Supplier::email() const { return m_email; }
QString Supplier::address() const { return m_address; }
double Supplier::balance() const { return m_balance; }
QDateTime Supplier::createdAt() const { return m_createdAt; }
QDateTime Supplier::updatedAt() const { return m_updatedAt; }

// Setters
void Supplier::setId(int id) { m_id = id; }
void Supplier::setName(const QString &name) { m_name = name; }
void Supplier::setPhone(const QString &phone) { m_phone = phone; }
void Supplier::setEmail(const QString &email) { m_email = email; }
void Supplier::setAddress(const QString &address) { m_address = address; }
void Supplier::setBalance(double balance) { m_balance = balance; }
void Supplier::setCreatedAt(const QDateTime &dateTime) { m_createdAt = dateTime; }
void Supplier::setUpdatedAt(const QDateTime &dateTime) { m_updatedAt = dateTime; }

// Business logic
bool Supplier::hasCredit() const
{
    return m_balance > 0;
}
