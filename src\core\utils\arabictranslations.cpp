#include "arabictranslations.h"

QHash<QString, QString> ArabicTranslations::m_translations;
bool ArabicTranslations::m_initialized = false;

QString ArabicTranslations::translate(const QString &english)
{
    if (!m_initialized) {
        initialize();
    }
    
    return m_translations.value(english, english);
}

void ArabicTranslations::initialize()
{
    if (m_initialized) {
        return;
    }
    
    // Main Window
    m_translations["Accountant - Inventory & Sales Management"] = "المحاسب - إدارة المخزون والمبيعات";
    m_translations["Dashboard"] = "لوحة التحكم";
    m_translations["Inventory"] = "المخزون";
    m_translations["Sales"] = "المبيعات";
    m_translations["Customers"] = "العملاء";
    m_translations["Suppliers"] = "الموردين";
    m_translations["Reports"] = "التقارير";
    
    // Menu items
    m_translations["&File"] = "&ملف";
    m_translations["E&xit"] = "&خروج";
    m_translations["&View"] = "&عرض";
    m_translations["&Dark Mode"] = "&الوضع الداكن";
    m_translations["&Language"] = "&اللغة";
    m_translations["&English"] = "&English";
    m_translations["&Arabic - العربية"] = "&العربية - Arabic";
    m_translations["&Tools"] = "&أدوات";
    m_translations["&Settings"] = "&الإعدادات";
    m_translations["&Help"] = "&مساعدة";
    m_translations["&About"] = "&حول";
    
    // Status messages
    m_translations["Ready"] = "جاهز";
    m_translations["Inventory Management"] = "إدارة المخزون";
    m_translations["Sales Management"] = "إدارة المبيعات";
    m_translations["Customer Management"] = "إدارة العملاء";
    m_translations["Supplier Management"] = "إدارة الموردين";
    
    // Dashboard
    m_translations["Total Sales"] = "إجمالي المبيعات";
    m_translations["Total Purchases"] = "إجمالي المشتريات";
    m_translations["Total Customers"] = "إجمالي العملاء";
    m_translations["Total Suppliers"] = "إجمالي الموردين";
    m_translations["Sales Overview"] = "نظرة عامة على المبيعات";
    m_translations["Inventory Status"] = "حالة المخزون";
    m_translations["Recent Activity"] = "النشاط الأخير";
    m_translations["Low Stock Alerts"] = "تنبيهات نفاد المخزون";
    
    // Inventory
    m_translations["Add Product"] = "إضافة منتج";
    m_translations["Edit"] = "تعديل";
    m_translations["Delete"] = "حذف";
    m_translations["Refresh"] = "تحديث";
    m_translations["Search:"] = "بحث:";
    m_translations["Enter product name or code"] = "أدخل اسم المنتج أو الكود";
    m_translations["Category:"] = "الفئة:";
    m_translations["All Categories"] = "جميع الفئات";
    m_translations["Search"] = "بحث";
    m_translations["ID"] = "المعرف";
    m_translations["Code"] = "الكود";
    m_translations["Name"] = "الاسم";
    m_translations["Category"] = "الفئة";
    m_translations["Purchase Price"] = "سعر الشراء";
    m_translations["Selling Price"] = "سعر البيع";
    m_translations["Quantity"] = "الكمية";
    
    // Sales
    m_translations["Create Invoice"] = "إنشاء فاتورة";
    m_translations["View/Edit"] = "عرض/تعديل";
    m_translations["Invoice Number"] = "رقم الفاتورة";
    m_translations["Customer"] = "العميل";
    m_translations["Date"] = "التاريخ";
    m_translations["Total Amount"] = "المبلغ الإجمالي";
    m_translations["Paid Amount"] = "المبلغ المدفوع";
    m_translations["Status"] = "الحالة";
    
    // Invoice Dialog
    m_translations["New Sales Invoice"] = "فاتورة مبيعات جديدة";
    m_translations["Edit Sales Invoice"] = "تعديل فاتورة المبيعات";
    m_translations["Invoice Information"] = "معلومات الفاتورة";
    m_translations["Invoice Number:"] = "رقم الفاتورة:";
    m_translations["Customer:"] = "العميل:";
    m_translations["Date:"] = "التاريخ:";
    m_translations["Status:"] = "الحالة:";
    m_translations["Items"] = "العناصر";
    m_translations["Product"] = "المنتج";
    m_translations["Unit Price"] = "سعر الوحدة";
    m_translations["Total"] = "الإجمالي";
    m_translations["Add Item"] = "إضافة عنصر";
    m_translations["Remove Item"] = "إزالة عنصر";
    m_translations["Totals"] = "الإجماليات";
    m_translations["Subtotal:"] = "المجموع الفرعي:";
    m_translations["Discount:"] = "الخصم:";
    m_translations["Tax:"] = "الضريبة:";
    m_translations["Total:"] = "الإجمالي:";
    m_translations["Paid Amount:"] = "المبلغ المدفوع:";
    m_translations["Remaining:"] = "المتبقي:";
    m_translations["Notes"] = "ملاحظات";
    
    // Common buttons and actions
    m_translations["OK"] = "موافق";
    m_translations["Cancel"] = "إلغاء";
    m_translations["Yes"] = "نعم";
    m_translations["No"] = "لا";
    m_translations["Save"] = "حفظ";
    m_translations["Close"] = "إغلاق";
    m_translations["Apply"] = "تطبيق";
    
    // Messages
    m_translations["Success"] = "نجح";
    m_translations["Error"] = "خطأ";
    m_translations["Warning"] = "تحذير";
    m_translations["Information"] = "معلومات";
    m_translations["Confirmation"] = "تأكيد";
    
    // Status values
    m_translations["Paid"] = "مدفوع";
    m_translations["Unpaid"] = "غير مدفوع";
    m_translations["Partial"] = "جزئي";
    
    // Categories
    m_translations["Electronics"] = "إلكترونيات";
    m_translations["Clothing"] = "ملابس";
    m_translations["Food"] = "طعام";
    m_translations["Books"] = "كتب";
    m_translations["Other"] = "أخرى";
    
    m_initialized = true;
}
