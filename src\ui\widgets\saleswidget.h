#ifndef SALESWIDGET_H
#define SALESWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTableView>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QSortFilterProxyModel>
#include <QLineEdit>
#include <QComboBox>
#include <QDateEdit>
#include <QMessageBox>
#include <QDebug>

#include "../../core/services/salesservice.h"
#include "../../core/models/invoice.h"
#include "iconhelper.h"

/**
 * @brief The SalesWidget class displays and manages sales
 */
class SalesWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SalesWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Load sales invoices from the database
     */
    void loadInvoices();

    /**
     * @brief Create a new sales invoice
     */
    void createInvoice();

    /**
     * @brief View the selected invoice
     */
    void viewInvoice();

    /**
     * @brief Delete the selected invoice
     */
    void deleteInvoice();

    /**
     * @brief Filter invoices based on search criteria
     */
    void filterInvoices();

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Update the invoice model with the current invoices
     */
    void updateInvoiceModel();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolbarLayout;
    QHBoxLayout *m_searchLayout;

    // Toolbar widgets
    QLabel *m_titleLabel;
    QPushButton *m_createButton;
    QPushButton *m_viewButton;
    QPushButton *m_deleteButton;
    QPushButton *m_refreshButton;

    // Search widgets
    QLineEdit *m_searchEdit;
    QComboBox *m_statusCombo;
    QDateEdit *m_fromDateEdit;
    QDateEdit *m_toDateEdit;
    QPushButton *m_searchButton;

    // Table view
    QTableView *m_invoicesTable;
    QStandardItemModel *m_invoicesModel;
    QSortFilterProxyModel *m_proxyModel;

    // Service
    SalesService m_salesService;

    // Current invoices
    QList<QSharedPointer<Invoice>> m_invoices;
};

#endif // SALESWIDGET_H
