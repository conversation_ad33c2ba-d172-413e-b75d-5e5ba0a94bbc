#ifndef INVOICE_H
#define INVOICE_H

#include <QString>
#include <QDateTime>
#include <QList>
#include <QSharedPointer>

class InvoiceItem;

/**
 * @brief The Invoice class represents an invoice in the system
 */
class Invoice
{
public:
    enum class Type {
        Sale,
        Purchase
    };
    
    enum class Status {
        Paid,
        Unpaid,
        Partial
    };
    
    Invoice();
    Invoice(int id, const QString &invoiceNumber, Type type, int customerId, int supplierId,
            double totalAmount, double discount, double tax, double paidAmount,
            Status status, const QString &notes);
    
    // Getters
    int id() const;
    QString invoiceNumber() const;
    Type type() const;
    int customerId() const;
    int supplierId() const;
    double totalAmount() const;
    double discount() const;
    double tax() const;
    double paidAmount() const;
    Status status() const;
    QString notes() const;
    QDateTime createdAt() const;
    QList<QSharedPointer<InvoiceItem>> items() const;
    
    // Setters
    void setId(int id);
    void setInvoiceNumber(const QString &invoiceNumber);
    void setType(Type type);
    void setCustomerId(int customerId);
    void setSupplierId(int supplierId);
    void setTotalAmount(double totalAmount);
    void setDiscount(double discount);
    void setTax(double tax);
    void setPaidAmount(double paidAmount);
    void setStatus(Status status);
    void setNotes(const QString &notes);
    void setCreatedAt(const QDateTime &dateTime);
    void setItems(const QList<QSharedPointer<InvoiceItem>> &items);
    
    // Business logic
    double netAmount() const;
    double remainingAmount() const;
    void addItem(const QSharedPointer<InvoiceItem> &item);
    void removeItem(int itemId);
    void updateStatus();
    
    // Static helpers
    static QString typeToString(Type type);
    static Type typeFromString(const QString &typeStr);
    static QString statusToString(Status status);
    static Status statusFromString(const QString &statusStr);
    
private:
    int m_id;
    QString m_invoiceNumber;
    Type m_type;
    int m_customerId;
    int m_supplierId;
    double m_totalAmount;
    double m_discount;
    double m_tax;
    double m_paidAmount;
    Status m_status;
    QString m_notes;
    QDateTime m_createdAt;
    QList<QSharedPointer<InvoiceItem>> m_items;
};

/**
 * @brief The InvoiceItem class represents an item in an invoice
 */
class InvoiceItem
{
public:
    InvoiceItem();
    InvoiceItem(int id, int invoiceId, int productId, int quantity, double unitPrice, double totalPrice);
    
    // Getters
    int id() const;
    int invoiceId() const;
    int productId() const;
    int quantity() const;
    double unitPrice() const;
    double totalPrice() const;
    QDateTime createdAt() const;
    
    // Setters
    void setId(int id);
    void setInvoiceId(int invoiceId);
    void setProductId(int productId);
    void setQuantity(int quantity);
    void setUnitPrice(double unitPrice);
    void setTotalPrice(double totalPrice);
    void setCreatedAt(const QDateTime &dateTime);
    
    // Business logic
    void updateTotalPrice();
    
private:
    int m_id;
    int m_invoiceId;
    int m_productId;
    int m_quantity;
    double m_unitPrice;
    double m_totalPrice;
    QDateTime m_createdAt;
};

#endif // INVOICE_H
