#include "iconhelper.h"

QIcon IconHelper::createTextIcon(const QString &unicode, int size,
                                const QColor &color, const QColor &backgroundColor)
{
    QPixmap pixmap(size, size);
    pixmap.fill(backgroundColor);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // Set font
    QFont font;
    font.setFamily("Segoe UI Emoji, Apple Color Emoji, Noto Color Emoji");
    font.setPixelSize(size * 0.7); // 70% of icon size
    painter.setFont(font);
    painter.setPen(color);

    // Draw text centered
    QRect rect(0, 0, size, size);
    painter.drawText(rect, Qt::AlignCenter, unicode);

    return QIcon(pixmap);
}

QIcon IconHelper::createCircleIcon(const QColor &color, int size)
{
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setBrush(color);
    painter.setPen(Qt::NoPen);

    int margin = size * 0.1;
    painter.drawEllipse(margin, margin, size - 2 * margin, size - 2 * margin);

    return QIcon(pixmap);
}

QIcon IconHelper::createRoundedRectIcon(const QColor &color, int size)
{
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setBrush(color);
    painter.setPen(Qt::NoPen);

    int margin = size * 0.1;
    int radius = size * 0.2;
    painter.drawRoundedRect(margin, margin, size - 2 * margin, size - 2 * margin, radius, radius);

    return QIcon(pixmap);
}

QIcon IconHelper::getAddIcon()
{
    return createTextIcon("+", 24, QColor(46, 204, 113));
}

QIcon IconHelper::getEditIcon()
{
    return createTextIcon("✎", 24, QColor(52, 152, 219));
}

QIcon IconHelper::getDeleteIcon()
{
    return createTextIcon("×", 24, QColor(231, 76, 60));
}

QIcon IconHelper::getRefreshIcon()
{
    return createTextIcon("↻", 24, QColor(155, 89, 182));
}

QIcon IconHelper::getSearchIcon()
{
    return createTextIcon("🔍", 24, QColor(52, 73, 94));
}

QIcon IconHelper::getViewIcon()
{
    return createTextIcon("👁", 24, QColor(26, 188, 156));
}

QIcon IconHelper::getSaveIcon()
{
    return createTextIcon("💾", 24, QColor(46, 204, 113));
}

QIcon IconHelper::getPrintIcon()
{
    return createTextIcon("🖨", 24, QColor(52, 73, 94));
}

QIcon IconHelper::getDashboardIcon()
{
    return createTextIcon("📊", 24, QColor(52, 152, 219));
}

QIcon IconHelper::getInventoryIcon()
{
    return createTextIcon("📦", 24, QColor(230, 126, 34));
}

QIcon IconHelper::getSalesIcon()
{
    return createTextIcon("💰", 24, QColor(46, 204, 113));
}

QIcon IconHelper::getCustomersIcon()
{
    return createTextIcon("👥", 24, QColor(155, 89, 182));
}

QIcon IconHelper::getSuppliersIcon()
{
    return createTextIcon("🏭", 24, QColor(52, 73, 94));
}

QIcon IconHelper::getReportsIcon()
{
    return createTextIcon("📈", 24, QColor(231, 76, 60));
}
