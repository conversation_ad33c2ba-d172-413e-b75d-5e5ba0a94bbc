#ifndef DASHBOARDWIDGET_H
#define DASHBOARDWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QtCharts/QChart>
#include <QtCharts/QChartView>
#include <QtCharts/QBarSeries>
#include <QtCharts/QBarSet>
#include <QtCharts/QBarCategoryAxis>
#include <QtCharts/QValueAxis>
#include <QtCharts/QPieSeries>
#include <QtCharts/QLineSeries>
#include <QtCharts/QDateTimeAxis>
#include <QtCharts/QSplineSeries>
#include <QGraphicsDropShadowEffect>
#include <QTimer>
#include <QDateTime>
#include <QDebug>

#include "../../core/services/inventoryservice.h"
#include "../../core/services/salesservice.h"
#include "../../core/services/purchaseservice.h"

QT_CHARTS_USE_NAMESPACE

/**
 * @brief The DashboardWidget class displays the dashboard with summary information
 */
class DashboardWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DashboardWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Refresh the dashboard data
     */
    void refreshData();

    /**
     * @brief Navigate to inventory page
     */
    void goToInventory();

    /**
     * @brief Navigate to sales page
     */
    void goToSales();

    /**
     * @brief Navigate to customers page
     */
    void goToCustomers();

    /**
     * @brief Navigate to suppliers page
     */
    void goToSuppliers();

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create a summary card widget
     * @param title Card title
     * @param value Card value
     * @param icon Card icon
     * @param color Card color
     * @return Frame containing the card
     */
    QFrame* createSummaryCard(const QString &title, const QString &value,
                             const QString &icon, const QColor &color);

    /**
     * @brief Create the sales chart
     * @return Chart view widget
     */
    QChartView* createSalesChart();

    /**
     * @brief Create the inventory chart
     * @return Chart view widget
     */
    QChartView* createInventoryChart();

    /**
     * @brief Create the recent activity list
     * @return Frame containing the activity list
     */
    QFrame* createRecentActivity();

    /**
     * @brief Create the low stock alert list
     * @return Frame containing the low stock list
     */
    QFrame* createLowStockAlerts();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_summaryLayout;
    QHBoxLayout *m_chartsLayout;
    QHBoxLayout *m_listsLayout;

    // Summary cards
    QFrame *m_totalSalesCard;
    QFrame *m_totalPurchasesCard;
    QFrame *m_totalCustomersCard;
    QFrame *m_totalSuppliersCard;
    QLabel *m_totalSalesValue;
    QLabel *m_totalPurchasesValue;
    QLabel *m_totalCustomersValue;
    QLabel *m_totalSuppliersValue;

    // Charts
    QChartView *m_salesChartView;
    QChartView *m_inventoryChartView;

    // Lists
    QFrame *m_recentActivityFrame;
    QFrame *m_lowStockFrame;
    QVBoxLayout *m_recentActivityLayout;
    QVBoxLayout *m_lowStockLayout;

    // Services
    InventoryService m_inventoryService;
    SalesService m_salesService;
    PurchaseService m_purchaseService;

    // Timer for auto-refresh
    QTimer *m_refreshTimer;
};

#endif // DASHBOARDWIDGET_H
