#include "product.h"

Product::Product()
    : m_id(0),
      m_purchasePrice(0.0),
      m_sellingPrice(0.0),
      m_quantity(0),
      m_minQuantity(0)
{
}

Product::Product(int id, const QString &code, const QString &name, const QString &description,
                 const QString &category, double purchasePrice, double sellingPrice,
                 int quantity, int minQuantity)
    : m_id(id),
      m_code(code),
      m_name(name),
      m_description(description),
      m_category(category),
      m_purchasePrice(purchasePrice),
      m_sellingPrice(sellingPrice),
      m_quantity(quantity),
      m_minQuantity(minQuantity)
{
}

// Getters
int Product::id() const { return m_id; }
QString Product::code() const { return m_code; }
QString Product::name() const { return m_name; }
QString Product::description() const { return m_description; }
QString Product::category() const { return m_category; }
double Product::purchasePrice() const { return m_purchasePrice; }
double Product::sellingPrice() const { return m_sellingPrice; }
int Product::quantity() const { return m_quantity; }
int Product::minQuantity() const { return m_minQuantity; }
QDateTime Product::createdAt() const { return m_createdAt; }
QDateTime Product::updatedAt() const { return m_updatedAt; }

// Setters
void Product::setId(int id) { m_id = id; }
void Product::setCode(const QString &code) { m_code = code; }
void Product::setName(const QString &name) { m_name = name; }
void Product::setDescription(const QString &description) { m_description = description; }
void Product::setCategory(const QString &category) { m_category = category; }
void Product::setPurchasePrice(double price) { m_purchasePrice = price; }
void Product::setSellingPrice(double price) { m_sellingPrice = price; }
void Product::setQuantity(int quantity) { m_quantity = quantity; }
void Product::setMinQuantity(int quantity) { m_minQuantity = quantity; }
void Product::setCreatedAt(const QDateTime &dateTime) { m_createdAt = dateTime; }
void Product::setUpdatedAt(const QDateTime &dateTime) { m_updatedAt = dateTime; }

// Business logic
double Product::profit() const
{
    return m_sellingPrice - m_purchasePrice;
}

bool Product::isLowStock() const
{
    return m_quantity <= m_minQuantity;
}
