using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class PurchaseService
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly SupplierService _supplierService;

        public PurchaseService(DatabaseService dbService, ProductService productService, SupplierService supplierService)
        {
            _dbService = dbService;
            _productService = productService;
            _supplierService = supplierService;
        }

        public async Task<IEnumerable<Purchase>> GetAllPurchasesAsync()
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                ORDER BY p.Date DESC";

            var purchases = await _dbService.QueryAsync<Purchase>(sql);
            foreach (var purchase in purchases)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchases;
        }

        public async Task<IEnumerable<Purchase>> GetPurchasesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                WHERE p.Date BETWEEN @StartDate AND @EndDate
                ORDER BY p.Date DESC";

            var purchases = await _dbService.QueryAsync<Purchase>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            foreach (var purchase in purchases)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchases;
        }

        public async Task<IEnumerable<Purchase>> GetPurchasesBySupplierAsync(int supplierId)
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                WHERE p.SupplierId = @SupplierId
                ORDER BY p.Date DESC";

            var purchases = await _dbService.QueryAsync<Purchase>(sql, new { SupplierId = supplierId });

            foreach (var purchase in purchases)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchases;
        }

        public async Task<IEnumerable<Purchase>> SearchPurchasesAsync(string searchTerm)
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                WHERE p.InvoiceNumber LIKE @SearchTerm OR s.Name LIKE @SearchTerm
                ORDER BY p.Date DESC";

            var purchases = await _dbService.QueryAsync<Purchase>(sql, new { SearchTerm = $"%{searchTerm}%" });

            foreach (var purchase in purchases)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchases;
        }

        public async Task<Purchase> GetPurchaseByIdAsync(int id)
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                WHERE p.Id = @Id";

            var purchase = await _dbService.QuerySingleOrDefaultAsync<Purchase>(sql, new { Id = id });

            if (purchase != null)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchase;
        }

        public async Task<Purchase> GetPurchaseByInvoiceNumberAsync(string invoiceNumber)
        {
            const string sql = @"
                SELECT p.*, s.Name as SupplierName 
                FROM Purchases p
                LEFT JOIN Suppliers s ON p.SupplierId = s.Id
                WHERE p.InvoiceNumber = @InvoiceNumber";

            var purchase = await _dbService.QuerySingleOrDefaultAsync<Purchase>(sql, new { InvoiceNumber = invoiceNumber });

            if (purchase != null)
            {
                purchase.Items = new System.Collections.ObjectModel.ObservableCollection<PurchaseItem>(
                    await GetPurchaseItemsAsync(purchase.Id));
            }

            return purchase;
        }

        public async Task<IEnumerable<PurchaseItem>> GetPurchaseItemsAsync(int purchaseId)
        {
            const string sql = @"
                SELECT pi.*, p.Name as ProductName, p.Code as ProductCode
                FROM PurchaseItems pi
                INNER JOIN Products p ON pi.ProductId = p.Id
                WHERE pi.PurchaseId = @PurchaseId";

            return await _dbService.QueryAsync<PurchaseItem>(sql, new { PurchaseId = purchaseId });
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            // Format: PO-YYYYMMDD-XXXX where XXXX is a sequential number
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");
            string sql = $"SELECT COUNT(*) FROM Purchases WHERE InvoiceNumber LIKE 'PO-{datePrefix}-%'";
            int count = await _dbService.QuerySingleOrDefaultAsync<int>(sql);
            return $"PO-{datePrefix}-{(count + 1).ToString("D4")}";
        }

        public async Task<Purchase> AddPurchaseAsync(Purchase purchase)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Generate invoice number if not provided
                    if (string.IsNullOrEmpty(purchase.InvoiceNumber))
                    {
                        purchase.InvoiceNumber = await GenerateInvoiceNumberAsync();
                    }

                    // Set timestamps
                    purchase.CreatedAt = DateTime.Now;

                    // Insert purchase
                    var insertedPurchase = await _dbService.InsertAsync<Purchase>("Purchases", purchase);

                    // Insert purchase items
                    foreach (var item in purchase.Items)
                    {
                        item.PurchaseId = insertedPurchase.Id;
                        await _dbService.InsertAsync<PurchaseItem>("PurchaseItems", item);

                        // Update product stock
                        await _productService.UpdateProductStockAsync(item.ProductId, item.Quantity);
                    }

                    // Update supplier balance if payment status is not "Paid"
                    if (purchase.SupplierId.HasValue && purchase.PaymentStatus != "Paid")
                    {
                        await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, purchase.Total);
                    }

                    transaction.Complete();
                    return insertedPurchase;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> UpdatePurchaseAsync(Purchase purchase)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get original purchase to calculate differences
                    var originalPurchase = await GetPurchaseByIdAsync(purchase.Id);
                    if (originalPurchase == null)
                    {
                        return false;
                    }

                    // Set update timestamp
                    purchase.UpdatedAt = DateTime.Now;

                    // Update purchase
                    await _dbService.UpdateAsync("Purchases", purchase);

                    // Delete existing purchase items
                    await _dbService.ExecuteAsync("DELETE FROM PurchaseItems WHERE PurchaseId = @PurchaseId", new { PurchaseId = purchase.Id });

                    // Restore original product quantities
                    foreach (var item in originalPurchase.Items)
                    {
                        await _productService.UpdateProductStockAsync(item.ProductId, -item.Quantity);
                    }

                    // Insert new purchase items and update product quantities
                    foreach (var item in purchase.Items)
                    {
                        item.PurchaseId = purchase.Id;
                        await _dbService.InsertAsync<PurchaseItem>("PurchaseItems", item);
                        await _productService.UpdateProductStockAsync(item.ProductId, item.Quantity);
                    }

                    // Update supplier balance if payment status changed
                    if (purchase.SupplierId.HasValue && originalPurchase.PaymentStatus != purchase.PaymentStatus)
                    {
                        if (originalPurchase.PaymentStatus != "Paid" && purchase.PaymentStatus == "Paid")
                        {
                            // If changed from unpaid to paid, reduce supplier balance
                            await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, -originalPurchase.Total);
                        }
                        else if (originalPurchase.PaymentStatus == "Paid" && purchase.PaymentStatus != "Paid")
                        {
                            // If changed from paid to unpaid, increase supplier balance
                            await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, purchase.Total);
                        }
                    }

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> DeletePurchaseAsync(int id)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get purchase to restore product quantities and update supplier balance
                    var purchase = await GetPurchaseByIdAsync(id);
                    if (purchase == null)
                    {
                        return false;
                    }

                    // Restore product quantities
                    foreach (var item in purchase.Items)
                    {
                        await _productService.UpdateProductStockAsync(item.ProductId, -item.Quantity);
                    }

                    // Update supplier balance if payment status is not "Paid"
                    if (purchase.SupplierId.HasValue && purchase.PaymentStatus != "Paid")
                    {
                        await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, -purchase.Total);
                    }

                    // Delete purchase items (will be cascaded by foreign key constraint)
                    await _dbService.ExecuteAsync("DELETE FROM PurchaseItems WHERE PurchaseId = @PurchaseId", new { PurchaseId = id });

                    // Delete purchase
                    await _dbService.DeleteAsync("Purchases", id);

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> UpdatePurchasePaymentStatusAsync(int purchaseId, string paymentStatus)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get original purchase
                    var purchase = await GetPurchaseByIdAsync(purchaseId);
                    if (purchase == null)
                    {
                        return false;
                    }

                    // Update payment status
                    const string sql = @"
                        UPDATE Purchases 
                        SET PaymentStatus = @PaymentStatus, UpdatedAt = @UpdatedAt 
                        WHERE Id = @PurchaseId";

                    await _dbService.ExecuteAsync(sql, new
                    {
                        PurchaseId = purchaseId,
                        PaymentStatus = paymentStatus,
                        UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    });

                    // Update supplier balance if payment status changed
                    if (purchase.SupplierId.HasValue)
                    {
                        if (purchase.PaymentStatus != "Paid" && paymentStatus == "Paid")
                        {
                            // If changed from unpaid to paid, reduce supplier balance
                            await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, -purchase.Total);
                        }
                        else if (purchase.PaymentStatus == "Paid" && paymentStatus != "Paid")
                        {
                            // If changed from paid to unpaid, increase supplier balance
                            await _supplierService.UpdateSupplierBalanceAsync(purchase.SupplierId.Value, purchase.Total);
                        }
                    }

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<decimal> GetTotalPurchasesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "WHERE Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $"SELECT SUM(Total) FROM Purchases {dateFilter}";

            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql, new
            {
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });

            return result ?? 0;
        }

        public async Task<IEnumerable<PurchaseSummaryByMonth>> GetPurchaseSummaryByMonthAsync(int year)
        {
            const string sql = @"
                SELECT strftime('%m', Date) as Month, SUM(Total) as TotalAmount, COUNT(*) as PurchaseCount
                FROM Purchases
                WHERE strftime('%Y', Date) = @Year
                GROUP BY strftime('%m', Date)
                ORDER BY Month";

            return await _dbService.QueryAsync<PurchaseSummaryByMonth>(sql, new { Year = year.ToString() });
        }

        public async Task<IEnumerable<PurchaseSummaryByProduct>> GetPurchaseSummaryByProductAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT p.Id, p.Name, p.Code, SUM(pi.Quantity) as TotalQuantity, SUM(pi.Total) as TotalAmount
                FROM PurchaseItems pi
                INNER JOIN Products p ON pi.ProductId = p.Id
                INNER JOIN Purchases pu ON pi.PurchaseId = pu.Id
                WHERE pu.Date BETWEEN @StartDate AND @EndDate
                GROUP BY p.Id, p.Name, p.Code
                ORDER BY TotalAmount DESC";

            return await _dbService.QueryAsync<PurchaseSummaryByProduct>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });
        }
    }

    public class PurchaseSummaryByMonth
    {
        public string Month { get; set; }
        public decimal TotalAmount { get; set; }
        public int PurchaseCount { get; set; }

        public string MonthName => int.TryParse(Month, out int monthNumber) 
            ? new DateTime(2000, monthNumber, 1).ToString("MMMM") 
            : Month;
    }

    public class PurchaseSummaryByProduct
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
    }
}