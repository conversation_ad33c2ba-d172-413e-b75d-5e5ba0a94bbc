# برنامج إدارة المخزون والمبيعات (Accountant)

برنامج لإدارة المخزون والمبيعات وحسابات الموردين والعملاء مبني باستخدام C++ و Qt.

## المميزات

- إدارة المخزون (إضافة، تعديل، حذف المنتجات)
- إدارة المبيعات والفواتير
- إدارة العملاء والموردين
- تقارير متنوعة (مبيعات، مخزون، عملاء، موردين)
- واجهة مستخدم عصرية مع دعم للوضع الداكن والفاتح

## متطلبات التشغيل

### الطريقة 1: باستخدام Docker (موصى بها)

- Docker
- X Server (للواجهة الرسومية)

### الطريقة 2: التثبيت المباشر

- Qt 6.x
- CMake 3.14+
- مترجم C++ (GCC/MinGW/MSVC)

## طريقة التشغيل باستخدام Docker

### 1. تثبيت وإعداد X Server (للواجهة الرسومية)

#### على Windows:
1. قم بتثبيت [VcXsrv](https://sourceforge.net/projects/vcxsrv/)
2. قم بتشغيل XLaunch مع الإعدادات التالية:
   - اختر "Multiple windows"
   - اختر "Start no client"
   - حدد "Disable access control"
   - انقر على "Finish"

#### على Linux:
- X Server مثبت افتراضيًا، تأكد من تفعيل الوصول:
  ```
  xhost +local:docker
  ```

#### على macOS:
1. قم بتثبيت [XQuartz](https://www.xquartz.org/)
2. قم بتشغيل XQuartz
3. في إعدادات XQuartz، فعّل "Allow connections from network clients"
4. أعد تشغيل XQuartz وقم بتنفيذ:
   ```
   xhost +localhost
   ```

### 2. تشغيل البرنامج

قم بتنفيذ الأمر التالي:

```bash
./run.sh
```

أو يمكنك تنفيذ الأوامر يدويًا:

```bash
# السماح بالاتصال من Docker إلى X Server
xhost +local:docker

# بناء وتشغيل الحاوية
docker-compose up --build
```

## طريقة التشغيل بدون Docker

### 1. تثبيت المتطلبات

قم بتثبيت Qt 6.x وCMake وأدوات البناء المناسبة لنظام التشغيل الخاص بك.

### 2. بناء المشروع

```bash
mkdir build
cd build
cmake ..
make
```

### 3. تشغيل البرنامج

```bash
./AccountantApp
```

## هيكل المشروع

- `src/core/`: النواة الأساسية (قاعدة البيانات، النماذج، الخدمات)
- `src/ui/`: واجهة المستخدم (النوافذ، العناصر، الموارد)
- `src/python/`: سكريبتات Python للتحليلات والتقارير (مستقبلي)
- `database/`: ملفات قاعدة البيانات
- `docs/`: التوثيق
- `tests/`: اختبارات الوحدة والتكامل

## المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات
4. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب Pull Request
