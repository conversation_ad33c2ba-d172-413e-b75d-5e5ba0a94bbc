# 📊 برنامج إدارة المخزون والمبيعات الحديث

## ✨ التحديثات الجديدة
- 🎨 **واجهة حديثة ومتطورة** مع تصميم عصري
- 🌙 **دعم الوضع الداكن والفاتح** المحسن
- 🎯 **أزرار تفاعلية** مع رموز تعبيرية وألوان متدرجة
- 📱 **بطاقات ملخص محدثة** مع تأثيرات بصرية
- 🔧 **واجهة إنشاء الفواتير** كاملة وجاهزة للاستخدام

## المتطلبات
1. Docker Desktop مثبت ويعمل
2. X Server للواجهة الرسومية:
   - **Windows**: VcXsrv
   - **Linux**: X Server (مثبت افتراضياً)
   - **macOS**: XQuartz

## خطوات التشغيل على Windows

### 1. تثبيت وتشغيل VcXsrv
1. قم بتنزيل VcXsrv من: https://sourceforge.net/projects/vcxsrv/
2. قم بتثبيته وتشغيل XLaunch
3. اختر الإعدادات التالية:
   - **Display settings**: Multiple windows
   - **Client startup**: Start no client
   - **Extra settings**: ✅ Disable access control (مهم جداً!)
4. انقر على Finish

### 2. تشغيل البرنامج

**الطريقة السريعة:**
```cmd
run_simple.bat
```

**أو الطريقة التقليدية:**
```cmd
run_windows.bat
```

**أو يدوياً:**
```cmd
docker-compose -f docker-compose.windows.yml up --build
```

## خطوات التشغيل على Linux
```bash
chmod +x run.sh
./run.sh
```

## خطوات التشغيل على macOS
1. تثبيت XQuartz من: https://www.xquartz.org/
2. تشغيل XQuartz وتفعيل "Allow connections from network clients"
3. تنفيذ:
```bash
xhost +localhost
./run.sh
```

## استخدام البرنامج

### إدارة المخزون
- انقر على "Inventory" في الشريط الجانبي
- استخدم "Add Product" لإضافة منتجات جديدة
- يمكنك تعديل أو حذف المنتجات الموجودة

### إدارة العملاء
- انقر على "Customers" في الشريط الجانبي
- استخدم "Add Customer" لإضافة عملاء جدد

### إدارة المبيعات 💰
- انقر على "💰 Sales" في الشريط الجانبي
- استخدم "📄 Create Invoice" لإنشاء فاتورة مبيعات جديدة
- اختر العميل من القائمة المنسدلة
- أضف المنتجات باستخدام "➕ Add Item"
- يمكنك تعديل الخصم والضريبة والمبلغ المدفوع
- احفظ الفاتورة وسيتم تحديث المخزون تلقائياً

### لوحة التحكم
- انقر على "Dashboard" لرؤية ملخص البيانات
- عرض الإحصائيات والرسوم البيانية

## حل المشاكل الشائعة

### البرنامج لا يظهر
- تأكد من تشغيل X Server (VcXsrv على Windows)
- تأكد من تفعيل "Disable access control"
- تحقق من أن Docker يعمل بشكل صحيح

### خطأ في البناء
- تأكد من اتصال الإنترنت (لتنزيل الحزم)
- جرب حذف الصور القديمة: `docker system prune -a`
- أعد تشغيل Docker Desktop

### البرنامج بطيء
- البناء الأول يستغرق وقتاً طويلاً (5-10 دقائق)
- التشغيلات اللاحقة ستكون أسرع بكثير

## الميزات المتاحة حالياً
✅ إدارة المنتجات (إضافة، تعديل، حذف، بحث)
✅ إدارة العملاء (إضافة، تعديل، حذف، بحث)
✅ إدارة الموردين (إضافة، تعديل، حذف، بحث)
✅ إنشاء فواتير المبيعات
✅ تعديل فواتير المبيعات
✅ حذف فواتير المبيعات
✅ لوحة تحكم مع إحصائيات أساسية
✅ تقارير أساسية
✅ دعم الوضع الداكن والفاتح

## الميزات قيد التطوير
🔄 إدارة المشتريات
🔄 إدارة المدفوعات
🔄 تقارير متقدمة
🔄 تصدير البيانات
🔄 طباعة الفواتير
🔄 نظام الباركود
