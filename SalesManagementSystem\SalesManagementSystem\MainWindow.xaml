<mah:MetroWindow x:Class="SalesManagementSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SalesManagementSystem"
        xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{DynamicResource AppTitle}" 
        Height="720" Width="1280"
        WindowStartupLocation="CenterScreen"
        GlowBrush="{DynamicResource AccentColorBrush}"
        BorderThickness="1"
        WindowTitleBrush="{DynamicResource PrimaryHueMidBrush}"
        NonActiveWindowTitleBrush="{DynamicResource PrimaryHueLightBrush}"
        TitleCharacterCasing="Normal">

    <mah:MetroWindow.LeftWindowCommands>
        <mah:WindowCommands>
            <Button Click="ToggleLanguage_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Translate" />
                    <TextBlock Margin="4,0,0,0" Text="{DynamicResource LanguageToggle}" />
                </StackPanel>
            </Button>
        </mah:WindowCommands>
    </mah:MetroWindow.LeftWindowCommands>

    <mah:MetroWindow.RightWindowCommands>
        <mah:WindowCommands>
            <Button Click="ToggleTheme_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ThemeLightDark" />
                    <TextBlock Margin="4,0,0,0" Text="{DynamicResource ThemeToggle}" />
                </StackPanel>
            </Button>
        </mah:WindowCommands>
    </mah:MetroWindow.RightWindowCommands>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- Left Navigation Panel -->
        <Grid Grid.Column="0" Background="{DynamicResource PrimaryHueMidBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="120" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- App Logo and Title -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Image Width="48" Height="48" Source="/Assets/Images/app_icon.ico" />
                <TextBlock Text="{DynamicResource AppTitle}" FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0" />
            </StackPanel>

            <!-- Navigation Menu -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <Button x:Name="DashboardButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ViewDashboard" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Dashboard}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="ProductsButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Package" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Products}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="SalesButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CartOutline" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Sales}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="CustomersButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountMultipleOutline" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Customers}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="SuppliersButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TruckOutline" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Suppliers}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="EmployeesButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountTie" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Employees}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="ExpensesButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CashMultiple" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Expenses}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>

                    <Button x:Name="ReportsButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartBar" Width="24" Height="24" />
                            <TextBlock Text="{DynamicResource Reports}" Margin="10,0,0,0" />
                        </StackPanel>
                    </Button>
                </StackPanel>
            </ScrollViewer>

            <!-- Settings Button -->
            <Button Grid.Row="2" x:Name="SettingsButton" Style="{StaticResource MenuButtonStyle}" Click="NavigationButton_Click" Margin="0,0,0,10">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cog" Width="24" Height="24" />
                    <TextBlock Text="{DynamicResource Settings}" Margin="10,0,0,0" />
                </StackPanel>
            </Button>
        </Grid>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Page Header -->
            <Grid Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}">
                <Grid.Effect>
                    <DropShadowEffect BlurRadius="5" ShadowDepth="1" Direction="270" Opacity="0.3" />
                </Grid.Effect>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0,0,0">
                    <TextBlock x:Name="PageTitle" Text="{DynamicResource Dashboard}" FontSize="24" FontWeight="Medium" />
                </StackPanel>
            </Grid>

            <!-- Page Content -->
            <Frame x:Name="MainFrame" Grid.Row="1" NavigationUIVisibility="Hidden" Background="{DynamicResource MaterialDesignBackground}" />
        </Grid>
    </Grid>
</mah:MetroWindow>