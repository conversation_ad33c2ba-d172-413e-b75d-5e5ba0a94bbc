#ifndef TRANSLATIONMANAGER_H
#define TRANSLATIONMANAGER_H

#include <QObject>
#include <QTranslator>
#include <QApplication>
#include <QLocale>
#include <QDir>
#include <QSettings>
#include <QDebug>

/**
 * @brief The TranslationManager class handles application translations and RTL support
 */
class TranslationManager : public QObject
{
    Q_OBJECT
    
public:
    enum Language {
        English,
        Arabic
    };
    
    explicit TranslationManager(QObject *parent = nullptr);
    ~TranslationManager();
    
    /**
     * @brief Initialize the translation system
     * @param app Application instance
     */
    void initialize(QApplication *app);
    
    /**
     * @brief Set the application language
     * @param language Language to set
     * @return true if successful
     */
    bool setLanguage(Language language);
    
    /**
     * @brief Get current language
     * @return Current language
     */
    Language currentLanguage() const;
    
    /**
     * @brief Check if current language is RTL
     * @return true if RTL
     */
    bool isRTL() const;
    
    /**
     * @brief Get language name
     * @param language Language enum
     * @return Language name in native script
     */
    static QString getLanguageName(Language language);
    
    /**
     * @brief Get available languages
     * @return List of available languages
     */
    static QList<Language> getAvailableLanguages();
    
    /**
     * @brief Load language from settings
     */
    void loadLanguageFromSettings();
    
    /**
     * @brief Save language to settings
     */
    void saveLanguageToSettings();
    
signals:
    /**
     * @brief Emitted when language changes
     */
    void languageChanged();
    
private:
    /**
     * @brief Load translation file
     * @param language Language to load
     * @return true if successful
     */
    bool loadTranslation(Language language);
    
    /**
     * @brief Setup RTL layout
     * @param enable Enable RTL layout
     */
    void setupRTL(bool enable);
    
    QApplication *m_app;
    QTranslator *m_translator;
    QTranslator *m_qtTranslator;
    Language m_currentLanguage;
};

#endif // TRANSLATIONMANAGER_H
