<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- App Strings -->
    <system:String x:Key="AppTitle">Sales Management System</system:String>
    <system:String x:Key="LanguageToggle">العربية</system:String>
    <system:String x:Key="ThemeToggle">Toggle Theme</system:String>
    
    <!-- Navigation Menu -->
    <system:String x:Key="Dashboard">Dashboard</system:String>
    <system:String x:Key="Products">Products</system:String>
    <system:String x:Key="Sales">Sales</system:String>
    <system:String x:Key="Customers">Customers</system:String>
    <system:String x:Key="Suppliers">Suppliers</system:String>
    <system:String x:Key="Employees">Employees</system:String>
    <system:String x:Key="Expenses">Expenses</system:String>
    <system:String x:Key="Reports">Reports</system:String>
    <system:String x:Key="Settings">Settings</system:String>
    
    <!-- Dashboard -->
    <system:String x:Key="TotalSales">Total Sales</system:String>
    <system:String x:Key="TotalExpenses">Total Expenses</system:String>
    <system:String x:Key="NetProfit">Net Profit</system:String>
    <system:String x:Key="LowStockItems">Low Stock Items</system:String>
    <system:String x:Key="RecentSales">Recent Sales</system:String>
    <system:String x:Key="TopSellingProducts">Top Selling Products</system:String>
    <system:String x:Key="SalesOverview">Sales Overview</system:String>
    
    <!-- Products -->
    <system:String x:Key="AddProduct">Add Product</system:String>
    <system:String x:Key="EditProduct">Edit Product</system:String>
    <system:String x:Key="DeleteProduct">Delete Product</system:String>
    <system:String x:Key="ProductCode">Product Code</system:String>
    <system:String x:Key="ProductName">Product Name</system:String>
    <system:String x:Key="ProductDescription">Product Description</system:String>
    <system:String x:Key="ProductCategory">Product Category</system:String>
    <system:String x:Key="PurchasePrice">Purchase Price</system:String>
    <system:String x:Key="SellingPrice">Selling Price</system:String>
    <system:String x:Key="Quantity">Quantity</system:String>
    <system:String x:Key="MinQuantity">Min Quantity</system:String>
    
    <!-- Sales -->
    <system:String x:Key="NewSale">New Sale</system:String>
    <system:String x:Key="SaleDetails">Sale Details</system:String>
    <system:String x:Key="InvoiceNumber">Invoice Number</system:String>
    <system:String x:Key="InvoiceDate">Invoice Date</system:String>
    <system:String x:Key="Customer">Customer</system:String>
    <system:String x:Key="Discount">Discount</system:String>
    <system:String x:Key="Tax">Tax</system:String>
    <system:String x:Key="Total">Total</system:String>
    <system:String x:Key="PaymentMethod">Payment Method</system:String>
    <system:String x:Key="PaymentStatus">Payment Status</system:String>
    <system:String x:Key="Print">Print</system:String>
    
    <!-- Customers -->
    <system:String x:Key="AddCustomer">Add Customer</system:String>
    <system:String x:Key="EditCustomer">Edit Customer</system:String>
    <system:String x:Key="DeleteCustomer">Delete Customer</system:String>
    <system:String x:Key="CustomerName">Customer Name</system:String>
    <system:String x:Key="Phone">Phone</system:String>
    <system:String x:Key="Email">Email</system:String>
    <system:String x:Key="Address">Address</system:String>
    <system:String x:Key="Balance">Balance</system:String>
    <system:String x:Key="CustomerHistory">Customer History</system:String>
    
    <!-- Suppliers -->
    <system:String x:Key="AddSupplier">Add Supplier</system:String>
    <system:String x:Key="EditSupplier">Edit Supplier</system:String>
    <system:String x:Key="DeleteSupplier">Delete Supplier</system:String>
    <system:String x:Key="SupplierName">Supplier Name</system:String>
    <system:String x:Key="SupplierHistory">Supplier History</system:String>
    
    <!-- Employees -->
    <system:String x:Key="AddEmployee">Add Employee</system:String>
    <system:String x:Key="EditEmployee">Edit Employee</system:String>
    <system:String x:Key="DeleteEmployee">Delete Employee</system:String>
    <system:String x:Key="EmployeeName">Employee Name</system:String>
    <system:String x:Key="Position">Position</system:String>
    <system:String x:Key="Salary">Salary</system:String>
    <system:String x:Key="HireDate">Hire Date</system:String>
    <system:String x:Key="Attendance">Attendance</system:String>
    <system:String x:Key="Performance">Performance</system:String>
    
    <!-- Expenses -->
    <system:String x:Key="AddExpense">Add Expense</system:String>
    <system:String x:Key="EditExpense">Edit Expense</system:String>
    <system:String x:Key="DeleteExpense">Delete Expense</system:String>
    <system:String x:Key="ExpenseTitle">Expense Title</system:String>
    <system:String x:Key="ExpenseCategory">Expense Category</system:String>
    <system:String x:Key="Amount">Amount</system:String>
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="Notes">Notes</system:String>
    
    <!-- Reports -->
    <system:String x:Key="SalesReport">Sales Report</system:String>
    <system:String x:Key="InventoryReport">Inventory Report</system:String>
    <system:String x:Key="CustomersReport">Customers Report</system:String>
    <system:String x:Key="SuppliersReport">Suppliers Report</system:String>
    <system:String x:Key="EmployeesReport">Employees Report</system:String>
    <system:String x:Key="ExpensesReport">Expenses Report</system:String>
    <system:String x:Key="ProfitLossReport">Profit &amp; Loss Report</system:String>
    <system:String x:Key="GenerateReport">Generate Report</system:String>
    <system:String x:Key="ExportReport">Export Report</system:String>
    
    <!-- Settings -->
    <system:String x:Key="GeneralSettings">General Settings</system:String>
    <system:String x:Key="UserManagement">User Management</system:String>
    <system:String x:Key="DatabaseSettings">Database Settings</system:String>
    <system:String x:Key="Backup">Backup</system:String>
    <system:String x:Key="Restore">Restore</system:String>
    <system:String x:Key="Language">Language</system:String>
    <system:String x:Key="Theme">Theme</system:String>
    
    <!-- Common -->
    <system:String x:Key="Save">Save</system:String>
    <system:String x:Key="Cancel">Cancel</system:String>
    <system:String x:Key="Delete">Delete</system:String>
    <system:String x:Key="Edit">Edit</system:String>
    <system:String x:Key="Add">Add</system:String>
    <system:String x:Key="Search">Search</system:String>
    <system:String x:Key="Filter">Filter</system:String>
    <system:String x:Key="Refresh">Refresh</system:String>
    <system:String x:Key="Yes">Yes</system:String>
    <system:String x:Key="No">No</system:String>
    <system:String x:Key="Ok">Ok</system:String>
    <system:String x:Key="Close">Close</system:String>
    <system:String x:Key="Error">Error</system:String>
    <system:String x:Key="Warning">Warning</system:String>
    <system:String x:Key="Information">Information</system:String>
    <system:String x:Key="Success">Success</system:String>
    <system:String x:Key="Confirmation">Confirmation</system:String>
    <system:String x:Key="AreYouSure">Are you sure?</system:String>
    <system:String x:Key="Loading">Loading...</system:String>
    <system:String x:Key="NoData">No data available</system:String>
    
</ResourceDictionary>