services:
  accountant-app:
    build: .
    image: accountant-app
    container_name: accountant-app
    environment:
      - DISPLAY=host.docker.internal:0.0
      - QT_X11_NO_MITSHM=1
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix
      - ./database:/app/database
    # Use host network on Linux, bridge on Windows/Mac
    network_mode: "host"
    # Uncomment the following for Windows/Mac if host network doesn't work
    # extra_hosts:
    #  - "host.docker.internal:host-gateway"
