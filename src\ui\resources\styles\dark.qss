/* Modern Dark Mode Stylesheet */

/* Global */
QWidget {
    background-color: #1a1a1a;
    color: #ffffff;
    font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    font-size: 9pt;
}

/* Main Window */
QMainWindow {
    background-color: #0d1117;
    border: none;
}

/* Menu Bar */
QMenuBar {
    background-color: #161b22;
    color: #f0f6fc;
    border-bottom: 1px solid #30363d;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #21262d;
    color: #58a6ff;
}

QMenu {
    background-color: #161b22;
    color: #f0f6fc;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #21262d;
    color: #58a6ff;
}

/* Tool Bar */
QToolBar {
    background-color: #161b22;
    border: none;
    spacing: 8px;
    padding: 8px;
}

QToolBar::separator {
    background-color: #30363d;
    width: 1px;
    margin: 4px;
}

/* Status Bar */
QStatusBar {
    background-color: #161b22;
    color: #8b949e;
    border-top: 1px solid #30363d;
    padding: 4px;
}

/* Side Bar */
QFrame#sideBar {
    background-color: #0d1117;
    border-right: 1px solid #21262d;
    min-width: 220px;
    max-width: 220px;
}

QLabel#logoLabel {
    color: #58a6ff;
    font-size: 18pt;
    font-weight: bold;
    padding: 20px 0px;
}

QToolButton {
    background-color: transparent;
    color: #f0f6fc;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    text-align: left;
    font-size: 10pt;
    font-weight: 500;
    margin: 2px 8px;
    min-height: 20px;
}

QToolButton:hover {
    background-color: #21262d;
    color: #58a6ff;
}

QToolButton:checked {
    background-color: #1f6feb;
    color: #ffffff;
    font-weight: 600;
}

/* Buttons */
QPushButton {
    background-color: #238636;
    color: #ffffff;
    border: 1px solid #2ea043;
    border-radius: 6px;
    padding: 8px 16px;
    min-width: 80px;
    font-weight: 500;
    font-size: 9pt;
}

QPushButton:hover {
    background-color: #2ea043;
    border-color: #3fb950;
    transform: translateY(-1px);
}

QPushButton:pressed {
    background-color: #1a7f37;
    border-color: #238636;
    transform: translateY(0px);
}

QPushButton:disabled {
    background-color: #21262d;
    color: #6e7681;
    border-color: #30363d;
}

/* Primary Button */
QPushButton[class="primary"] {
    background-color: #1f6feb;
    border-color: #1f6feb;
}

QPushButton[class="primary"]:hover {
    background-color: #388bfd;
    border-color: #388bfd;
}

/* Danger Button */
QPushButton[class="danger"] {
    background-color: #da3633;
    border-color: #da3633;
}

QPushButton[class="danger"]:hover {
    background-color: #f85149;
    border-color: #f85149;
}

/* Line Edit */
QLineEdit {
    background-color: #0d1117;
    color: #f0f6fc;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 9pt;
    selection-background-color: #1f6feb;
}

QLineEdit:focus {
    border: 2px solid #1f6feb;
    background-color: #161b22;
}

QLineEdit:hover {
    border-color: #6e7681;
}

/* Combo Box */
QComboBox {
    background-color: #0d1117;
    color: #f0f6fc;
    border: 1px solid #30363d;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 9pt;
    min-width: 120px;
}

QComboBox:focus {
    border: 2px solid #1f6feb;
    background-color: #161b22;
}

QComboBox:hover {
    border-color: #6e7681;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 24px;
    border-left: 1px solid #30363d;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-color: #21262d;
}

QComboBox::down-arrow {
    image: none;
    border: 2px solid #8b949e;
    width: 6px;
    height: 6px;
    border-top: none;
    border-right: none;
    transform: rotate(45deg);
}

QComboBox QAbstractItemView {
    background-color: #161b22;
    color: #f0f6fc;
    border: 1px solid #30363d;
    border-radius: 6px;
    selection-background-color: #1f6feb;
    selection-color: #ffffff;
    padding: 4px;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox, QDateEdit {
    background-color: #3E3E40;
    color: #FFFFFF;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px;
}

QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border: 1px solid #0078D7;
}

/* Table View */
QTableView {
    background-color: #0d1117;
    color: #f0f6fc;
    gridline-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 8px;
    selection-background-color: #1f6feb;
}

QTableView::item {
    padding: 12px 8px;
    border: none;
}

QTableView::item:selected {
    background-color: #1f6feb;
    color: #ffffff;
}

QTableView::item:hover {
    background-color: #161b22;
}

QHeaderView::section {
    background-color: #161b22;
    color: #f0f6fc;
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid #21262d;
    font-weight: 600;
    font-size: 9pt;
}

QHeaderView::section:hover {
    background-color: #21262d;
}

QTableView::item:alternate {
    background-color: #161b22;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #3E3E40;
}

QTabBar::tab {
    background-color: #2D2D30;
    color: #FFFFFF;
    border: 1px solid #3E3E40;
    border-bottom: none;
    padding: 6px 12px;
}

QTabBar::tab:selected {
    background-color: #1E1E1E;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

/* Scroll Bar */
QScrollBar:vertical {
    background-color: #2D2D30;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #3E3E40;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #0078D7;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #2D2D30;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #3E3E40;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #0078D7;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Dialog */
QDialog {
    background-color: #2D2D30;
    color: #FFFFFF;
}

/* Group Box */
QGroupBox {
    border: 1px solid #3E3E40;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 8px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 3px;
}

/* Charts */
QChartView {
    background-color: #2D2D30;
}

/* Summary Cards */
QFrame#summaryCard {
    border-radius: 12px;
    border: 1px solid #30363d;
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #21262d, stop: 1 #161b22);
    padding: 20px;
    margin: 8px;
}

QFrame#summaryCard:hover {
    border-color: #6e7681;
    transform: translateY(-2px);
}

/* Modern Cards */
QFrame[class="card"] {
    background-color: #161b22;
    border: 1px solid #30363d;
    border-radius: 12px;
    padding: 16px;
    margin: 8px;
}

QFrame[class="card"]:hover {
    border-color: #6e7681;
    background-color: #21262d;
}

/* Group Boxes */
QGroupBox {
    border: 1px solid #30363d;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 16px;
    font-weight: 600;
    font-size: 10pt;
    color: #f0f6fc;
    background-color: #161b22;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 8px;
    background-color: #161b22;
    color: #58a6ff;
}
