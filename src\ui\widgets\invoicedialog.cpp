#include "invoicedialog.h"

InvoiceDialog::InvoiceDialog(QWidget *parent)
    : QDialog(parent),
      m_salesService(this),
      m_inventoryService(this),
      m_isEditing(false)
{
    m_invoice = QSharedPointer<Invoice>(new Invoice());
    createUI();
    loadCustomers();
    loadProducts();
    setupItemsTable();

    setWindowTitle(tr("New Sales Invoice"));
    resize(800, 600);
}

InvoiceDialog::InvoiceDialog(QSharedPointer<Invoice> invoice, QWidget *parent)
    : QDialog(parent),
      m_invoice(invoice),
      m_salesService(this),
      m_inventoryService(this),
      m_isEditing(true)
{
    createUI();
    loadCustomers();
    loadProducts();
    setupItemsTable();
    loadInvoiceData();

    setWindowTitle(tr("Edit Sales Invoice"));
    resize(800, 600);
}

QSharedPointer<Invoice> InvoiceDialog::getInvoice() const
{
    return m_invoice;
}

void InvoiceDialog::onCustomerChanged()
{
    int customerIndex = m_customerCombo->currentIndex();
    if (customerIndex > 0) { // Skip "Select Customer" option
        int customerId = m_customerCombo->currentData().toInt();
        m_invoice->setCustomerId(customerId);
    }
}

void InvoiceDialog::addItem()
{
    int row = m_itemsTable->rowCount();
    m_itemsTable->insertRow(row);

    // Product combo
    QComboBox *productCombo = new QComboBox();
    productCombo->addItem(tr("Select Product"), 0);
    for (const QSharedPointer<Product> &product : m_products) {
        productCombo->addItem(product->name(), product->id());
    }
    m_itemsTable->setCellWidget(row, 0, productCombo);

    // Quantity spin box
    QSpinBox *quantitySpin = new QSpinBox();
    quantitySpin->setMinimum(1);
    quantitySpin->setMaximum(10000);
    quantitySpin->setValue(1);
    m_itemsTable->setCellWidget(row, 1, quantitySpin);

    // Unit price spin box
    QDoubleSpinBox *priceSpin = new QDoubleSpinBox();
    priceSpin->setMinimum(0.0);
    priceSpin->setMaximum(999999.99);
    priceSpin->setDecimals(2);
    m_itemsTable->setCellWidget(row, 2, priceSpin);

    // Total (calculated)
    m_itemsTable->setItem(row, 3, new QTableWidgetItem("0.00"));

    // Connect signals
    connect(productCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            [this, row, productCombo, priceSpin]() {
                int productId = productCombo->currentData().toInt();
                if (productId > 0) {
                    QSharedPointer<Product> product = m_inventoryService.getProductById(productId);
                    if (product) {
                        priceSpin->setValue(product->sellingPrice());
                        onItemChanged(row, 2); // Update total
                    }
                }
            });

    connect(quantitySpin, QOverload<int>::of(&QSpinBox::valueChanged),
            [this, row]() { onItemChanged(row, 1); });

    connect(priceSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            [this, row]() { onItemChanged(row, 2); });
}

void InvoiceDialog::removeItem()
{
    int currentRow = m_itemsTable->currentRow();
    if (currentRow >= 0) {
        m_itemsTable->removeRow(currentRow);
        updateTotals();
    }
}

void InvoiceDialog::onItemChanged(int row, int column)
{
    Q_UNUSED(column)

    // Get widgets from the row
    QComboBox *productCombo = qobject_cast<QComboBox*>(m_itemsTable->cellWidget(row, 0));
    QSpinBox *quantitySpin = qobject_cast<QSpinBox*>(m_itemsTable->cellWidget(row, 1));
    QDoubleSpinBox *priceSpin = qobject_cast<QDoubleSpinBox*>(m_itemsTable->cellWidget(row, 2));
    QTableWidgetItem *totalItem = m_itemsTable->item(row, 3);

    if (productCombo && quantitySpin && priceSpin && totalItem) {
        double total = quantitySpin->value() * priceSpin->value();
        totalItem->setText(QString::number(total, 'f', 2));
        updateTotals();
    }
}

void InvoiceDialog::updateTotals()
{
    double subtotal = 0.0;

    // Calculate subtotal from all items
    for (int row = 0; row < m_itemsTable->rowCount(); ++row) {
        QTableWidgetItem *totalItem = m_itemsTable->item(row, 3);
        if (totalItem) {
            subtotal += totalItem->text().toDouble();
        }
    }

    m_subtotalSpin->setValue(subtotal);

    // Calculate total
    double discount = m_discountSpin->value();
    double tax = m_taxSpin->value();
    double total = subtotal - discount + tax;

    m_totalSpin->setValue(total);

    // Calculate remaining amount
    double paidAmount = m_paidAmountSpin->value();
    double remaining = total - paidAmount;

    m_remainingSpin->setValue(remaining);

    // Update invoice data
    m_invoice->setTotalAmount(subtotal);
    m_invoice->setDiscount(discount);
    m_invoice->setTax(tax);
    m_invoice->setPaidAmount(paidAmount);
}

bool InvoiceDialog::validateInvoice()
{
    // Check if customer is selected
    if (m_customerCombo->currentIndex() <= 0) {
        QMessageBox::warning(this, tr("Validation Error"), tr("Please select a customer."));
        return false;
    }

    // Check if there are items
    if (m_itemsTable->rowCount() == 0) {
        QMessageBox::warning(this, tr("Validation Error"), tr("Please add at least one item."));
        return false;
    }

    // Check if all items have valid products
    for (int row = 0; row < m_itemsTable->rowCount(); ++row) {
        QComboBox *productCombo = qobject_cast<QComboBox*>(m_itemsTable->cellWidget(row, 0));
        if (!productCombo || productCombo->currentData().toInt() <= 0) {
            QMessageBox::warning(this, tr("Validation Error"),
                                tr("Please select a product for all items."));
            return false;
        }
    }

    return true;
}

void InvoiceDialog::accept()
{
    if (!validateInvoice()) {
        return;
    }

    // Update invoice data
    m_invoice->setInvoiceNumber(m_invoiceNumberEdit->text());
    m_invoice->setCustomerId(m_customerCombo->currentData().toInt());
    m_invoice->setNotes(m_notesEdit->toPlainText());

    // Create invoice items
    QList<QSharedPointer<InvoiceItem>> items;
    for (int row = 0; row < m_itemsTable->rowCount(); ++row) {
        QComboBox *productCombo = qobject_cast<QComboBox*>(m_itemsTable->cellWidget(row, 0));
        QSpinBox *quantitySpin = qobject_cast<QSpinBox*>(m_itemsTable->cellWidget(row, 1));
        QDoubleSpinBox *priceSpin = qobject_cast<QDoubleSpinBox*>(m_itemsTable->cellWidget(row, 2));

        if (productCombo && quantitySpin && priceSpin) {
            QSharedPointer<InvoiceItem> item = QSharedPointer<InvoiceItem>(new InvoiceItem());
            item->setProductId(productCombo->currentData().toInt());
            item->setQuantity(quantitySpin->value());
            item->setUnitPrice(priceSpin->value());
            item->setTotalPrice(quantitySpin->value() * priceSpin->value());
            items.append(item);
        }
    }
    m_invoice->setItems(items);

    // Update status based on payment
    m_invoice->updateStatus();

    QDialog::accept();
}

void InvoiceDialog::createUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // Header section
    QGroupBox *headerGroup = new QGroupBox(tr("Invoice Information"));
    m_headerLayout = new QFormLayout(headerGroup);

    m_invoiceNumberEdit = new QLineEdit();
    m_invoiceNumberEdit->setPlaceholderText(tr("Auto-generated"));
    m_headerLayout->addRow(tr("Invoice Number:"), m_invoiceNumberEdit);

    m_customerCombo = new QComboBox();
    m_headerLayout->addRow(tr("Customer:"), m_customerCombo);

    m_dateEdit = new QDateEdit(QDate::currentDate());
    m_dateEdit->setCalendarPopup(true);
    m_headerLayout->addRow(tr("Date:"), m_dateEdit);

    m_statusCombo = new QComboBox();
    m_statusCombo->addItem(tr("Unpaid"), static_cast<int>(Invoice::Status::Unpaid));
    m_statusCombo->addItem(tr("Partial"), static_cast<int>(Invoice::Status::Partial));
    m_statusCombo->addItem(tr("Paid"), static_cast<int>(Invoice::Status::Paid));
    m_headerLayout->addRow(tr("Status:"), m_statusCombo);

    m_mainLayout->addWidget(headerGroup);

    // Items section
    QGroupBox *itemsGroup = new QGroupBox(tr("Items"));
    QVBoxLayout *itemsLayout = new QVBoxLayout(itemsGroup);

    // Items table
    m_itemsTable = new QTableWidget(0, 4);
    m_itemsTable->setHorizontalHeaderLabels({tr("Product"), tr("Quantity"), tr("Unit Price"), tr("Total")});
    m_itemsTable->horizontalHeader()->setStretchLastSection(true);
    m_itemsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    itemsLayout->addWidget(m_itemsTable);

    // Items buttons
    QHBoxLayout *itemsButtonLayout = new QHBoxLayout();
    m_addItemButton = new QPushButton("➕ Add Item");
    m_addItemButton->setProperty("class", "primary");
    m_removeItemButton = new QPushButton("➖ Remove Item");
    m_removeItemButton->setProperty("class", "danger");

    connect(m_addItemButton, &QPushButton::clicked, this, &InvoiceDialog::addItem);
    connect(m_removeItemButton, &QPushButton::clicked, this, &InvoiceDialog::removeItem);

    itemsButtonLayout->addWidget(m_addItemButton);
    itemsButtonLayout->addWidget(m_removeItemButton);
    itemsButtonLayout->addStretch();

    itemsLayout->addLayout(itemsButtonLayout);
    m_mainLayout->addWidget(itemsGroup);

    // Totals section
    QGroupBox *totalsGroup = new QGroupBox(tr("Totals"));
    QFormLayout *totalsLayout = new QFormLayout(totalsGroup);

    m_subtotalSpin = new QDoubleSpinBox();
    m_subtotalSpin->setReadOnly(true);
    m_subtotalSpin->setMaximum(999999.99);
    m_subtotalSpin->setDecimals(2);
    totalsLayout->addRow(tr("Subtotal:"), m_subtotalSpin);

    m_discountSpin = new QDoubleSpinBox();
    m_discountSpin->setMaximum(999999.99);
    m_discountSpin->setDecimals(2);
    connect(m_discountSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &InvoiceDialog::updateTotals);
    totalsLayout->addRow(tr("Discount:"), m_discountSpin);

    m_taxSpin = new QDoubleSpinBox();
    m_taxSpin->setMaximum(999999.99);
    m_taxSpin->setDecimals(2);
    connect(m_taxSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &InvoiceDialog::updateTotals);
    totalsLayout->addRow(tr("Tax:"), m_taxSpin);

    m_totalSpin = new QDoubleSpinBox();
    m_totalSpin->setReadOnly(true);
    m_totalSpin->setMaximum(999999.99);
    m_totalSpin->setDecimals(2);
    totalsLayout->addRow(tr("Total:"), m_totalSpin);

    m_paidAmountSpin = new QDoubleSpinBox();
    m_paidAmountSpin->setMaximum(999999.99);
    m_paidAmountSpin->setDecimals(2);
    connect(m_paidAmountSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &InvoiceDialog::updateTotals);
    totalsLayout->addRow(tr("Paid Amount:"), m_paidAmountSpin);

    m_remainingSpin = new QDoubleSpinBox();
    m_remainingSpin->setReadOnly(true);
    m_remainingSpin->setMaximum(999999.99);
    m_remainingSpin->setDecimals(2);
    totalsLayout->addRow(tr("Remaining:"), m_remainingSpin);

    m_mainLayout->addWidget(totalsGroup);

    // Notes section
    QGroupBox *notesGroup = new QGroupBox(tr("Notes"));
    QVBoxLayout *notesLayout = new QVBoxLayout(notesGroup);

    m_notesEdit = new QTextEdit();
    m_notesEdit->setMaximumHeight(80);
    notesLayout->addWidget(m_notesEdit);

    m_mainLayout->addWidget(notesGroup);

    // Buttons
    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &InvoiceDialog::accept);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);

    m_mainLayout->addWidget(m_buttonBox);

    // Connect customer combo
    connect(m_customerCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &InvoiceDialog::onCustomerChanged);
}

void InvoiceDialog::loadCustomers()
{
    m_customers = m_salesService.getAllCustomers();

    m_customerCombo->clear();
    m_customerCombo->addItem(tr("Select Customer"), 0);

    for (const QSharedPointer<Customer> &customer : m_customers) {
        m_customerCombo->addItem(customer->name(), customer->id());
    }
}

void InvoiceDialog::loadProducts()
{
    m_products = m_inventoryService.getAllProducts();

    m_productNames.clear();
    for (const QSharedPointer<Product> &product : m_products) {
        m_productNames.append(product->name());
    }
}

void InvoiceDialog::setupItemsTable()
{
    m_itemsTable->setColumnWidth(0, 200);
    m_itemsTable->setColumnWidth(1, 100);
    m_itemsTable->setColumnWidth(2, 100);
    m_itemsTable->setColumnWidth(3, 100);
}

void InvoiceDialog::loadInvoiceData()
{
    if (!m_invoice) return;

    m_invoiceNumberEdit->setText(m_invoice->invoiceNumber());

    // Set customer
    for (int i = 0; i < m_customerCombo->count(); ++i) {
        if (m_customerCombo->itemData(i).toInt() == m_invoice->customerId()) {
            m_customerCombo->setCurrentIndex(i);
            break;
        }
    }

    m_discountSpin->setValue(m_invoice->discount());
    m_taxSpin->setValue(m_invoice->tax());
    m_paidAmountSpin->setValue(m_invoice->paidAmount());
    m_notesEdit->setPlainText(m_invoice->notes());

    // Load items
    for (const QSharedPointer<InvoiceItem> &item : m_invoice->items()) {
        addItem();
        int row = m_itemsTable->rowCount() - 1;

        // Set product
        QComboBox *productCombo = qobject_cast<QComboBox*>(m_itemsTable->cellWidget(row, 0));
        if (productCombo) {
            for (int i = 0; i < productCombo->count(); ++i) {
                if (productCombo->itemData(i).toInt() == item->productId()) {
                    productCombo->setCurrentIndex(i);
                    break;
                }
            }
        }

        // Set quantity
        QSpinBox *quantitySpin = qobject_cast<QSpinBox*>(m_itemsTable->cellWidget(row, 1));
        if (quantitySpin) {
            quantitySpin->setValue(item->quantity());
        }

        // Set unit price
        QDoubleSpinBox *priceSpin = qobject_cast<QDoubleSpinBox*>(m_itemsTable->cellWidget(row, 2));
        if (priceSpin) {
            priceSpin->setValue(item->unitPrice());
        }

        // Update total
        onItemChanged(row, 2);
    }

    updateTotals();
}
