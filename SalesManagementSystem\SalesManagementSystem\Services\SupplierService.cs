using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SupplierService
    {
        private readonly DatabaseService _dbService;

        public SupplierService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            const string sql = "SELECT * FROM Suppliers ORDER BY Name";
            return await _dbService.QueryAsync<Supplier>(sql);
        }

        public async Task<IEnumerable<Supplier>> GetSuppliersWithOutstandingBalanceAsync()
        {
            const string sql = "SELECT * FROM Suppliers WHERE Balance > 0 ORDER BY Balance DESC";
            return await _dbService.QueryAsync<Supplier>(sql);
        }

        public async Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            const string sql = @"
                SELECT * FROM Suppliers 
                WHERE Name LIKE @SearchTerm OR Phone LIKE @SearchTerm OR Email LIKE @SearchTerm 
                ORDER BY Name";

            return await _dbService.QueryAsync<Supplier>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Supplier> GetSupplierByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Suppliers WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Supplier>(sql, new { Id = id });
        }

        public async Task<Supplier> AddSupplierAsync(Supplier supplier)
        {
            // Set timestamps
            supplier.CreatedAt = DateTime.Now;

            return await _dbService.InsertAsync<Supplier>("Suppliers", supplier);
        }

        public async Task<bool> UpdateSupplierAsync(Supplier supplier)
        {
            // Set update timestamp
            supplier.UpdatedAt = DateTime.Now;

            return await _dbService.UpdateAsync("Suppliers", supplier);
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            // Check if supplier has related purchases
            const string checkSql = "SELECT COUNT(*) FROM Purchases WHERE SupplierId = @Id";
            int purchaseCount = await _dbService.QuerySingleOrDefaultAsync<int>(checkSql, new { Id = id });

            if (purchaseCount > 0)
            {
                throw new InvalidOperationException("Cannot delete supplier with related purchases");
            }

            return await _dbService.DeleteAsync("Suppliers", id);
        }

        public async Task<bool> UpdateSupplierBalanceAsync(int supplierId, decimal amount)
        {
            const string sql = @"
                UPDATE Suppliers 
                SET Balance = Balance + @Amount, UpdatedAt = @UpdatedAt 
                WHERE Id = @SupplierId";

            return await _dbService.ExecuteAsync(sql, new
            {
                SupplierId = supplierId,
                Amount = amount,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            }) > 0;
        }

        public async Task<IEnumerable<Purchase>> GetSupplierPurchasesAsync(int supplierId)
        {
            const string sql = @"
                SELECT * FROM Purchases 
                WHERE SupplierId = @SupplierId 
                ORDER BY Date DESC";

            return await _dbService.QueryAsync<Purchase>(sql, new { SupplierId = supplierId });
        }

        public async Task<IEnumerable<Supplier>> GetTopSuppliersAsync(int count = 5)
        {
            const string sql = @"
                SELECT s.*, SUM(p.Total) as PurchaseTotal 
                FROM Suppliers s
                LEFT JOIN Purchases p ON s.Id = p.SupplierId
                GROUP BY s.Id
                ORDER BY PurchaseTotal DESC
                LIMIT @Count";

            return await _dbService.QueryAsync<Supplier>(sql, new { Count = count });
        }

        public async Task<decimal> GetTotalSupplierBalanceAsync()
        {
            const string sql = "SELECT SUM(Balance) FROM Suppliers";
            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql);
            return result ?? 0;
        }

        public async Task<bool> MakePaymentToSupplierAsync(int supplierId, decimal amount, string paymentMethod, string reference = null)
        {
            // Create a payment record and update supplier balance
            const string paymentSql = @"
                INSERT INTO SupplierPayments (SupplierId, Amount, PaymentMethod, Reference, Date, CreatedAt) 
                VALUES (@SupplierId, @Amount, @PaymentMethod, @Reference, @Date, @CreatedAt)";

            await _dbService.ExecuteAsync(paymentSql, new
            {
                SupplierId = supplierId,
                Amount = amount,
                PaymentMethod = paymentMethod,
                Reference = reference,
                Date = DateTime.Now.ToString("yyyy-MM-dd"),
                CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            // Update supplier balance (reduce balance by payment amount)
            return await UpdateSupplierBalanceAsync(supplierId, -amount);
        }
    }

    // This class is a placeholder for the Purchase model
    // It should be defined in a separate file when implementing the purchase functionality
    public class Purchase
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public DateTime Date { get; set; }
        public decimal Subtotal { get; set; }
        public decimal Discount { get; set; }
        public decimal Tax { get; set; }
        public decimal Total { get; set; }
        public string PaymentMethod { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}