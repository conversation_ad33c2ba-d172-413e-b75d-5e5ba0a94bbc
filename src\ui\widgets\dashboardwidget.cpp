#include "dashboardwidget.h"

DashboardWidget::DashboardWidget(QWidget *parent)
    : QWidget(parent),
      m_inventoryService(this),
      m_salesService(this),
      m_purchaseService(this)
{
    createUI();

    // Set up refresh timer
    m_refreshTimer = new QTimer(this);
    connect(m_refreshTimer, &QTimer::timeout, this, &DashboardWidget::refreshData);
    m_refreshTimer->start(60000); // Refresh every minute

    // Initial data load
    refreshData();
}

void DashboardWidget::refreshData()
{
    // Update summary cards
    int totalSales = m_salesService.getAllSalesInvoices().size();
    int totalPurchases = m_purchaseService.getAllPurchaseInvoices().size();
    int totalCustomers = m_salesService.getAllCustomers().size();
    int totalSuppliers = m_purchaseService.getAllSuppliers().size();

    m_totalSalesValue->setText(QString::number(totalSales));
    m_totalPurchasesValue->setText(QString::number(totalPurchases));
    m_totalCustomersValue->setText(QString::number(totalCustomers));
    m_totalSuppliersValue->setText(QString::number(totalSuppliers));

    // TODO: Update charts and lists with real data
}

void DashboardWidget::goToInventory()
{
    // Signal to main window to switch to inventory page
    // This will be implemented when connecting the widgets
}

void DashboardWidget::goToSales()
{
    // Signal to main window to switch to sales page
    // This will be implemented when connecting the widgets
}

void DashboardWidget::goToCustomers()
{
    // Signal to main window to switch to customers page
    // This will be implemented when connecting the widgets
}

void DashboardWidget::goToSuppliers()
{
    // Signal to main window to switch to suppliers page
    // This will be implemented when connecting the widgets
}

void DashboardWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(20);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    QLabel *titleLabel = new QLabel(tr("Dashboard"), this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(titleLabel);

    // Summary cards
    m_summaryLayout = new QHBoxLayout();
    m_summaryLayout->setSpacing(15);

    m_totalSalesCard = createSummaryCard(tr("Total Sales"), "0", ":/icons/sales.png", QColor(52, 152, 219));
    m_totalPurchasesCard = createSummaryCard(tr("Total Purchases"), "0", ":/icons/purchases.png", QColor(46, 204, 113));
    m_totalCustomersCard = createSummaryCard(tr("Total Customers"), "0", ":/icons/customers.png", QColor(155, 89, 182));
    m_totalSuppliersCard = createSummaryCard(tr("Total Suppliers"), "0", ":/icons/suppliers.png", QColor(230, 126, 34));

    m_summaryLayout->addWidget(m_totalSalesCard);
    m_summaryLayout->addWidget(m_totalPurchasesCard);
    m_summaryLayout->addWidget(m_totalCustomersCard);
    m_summaryLayout->addWidget(m_totalSuppliersCard);

    m_mainLayout->addLayout(m_summaryLayout);

    // Charts
    m_chartsLayout = new QHBoxLayout();
    m_chartsLayout->setSpacing(15);

    m_salesChartView = createSalesChart();
    m_inventoryChartView = createInventoryChart();

    m_chartsLayout->addWidget(m_salesChartView);
    m_chartsLayout->addWidget(m_inventoryChartView);

    m_mainLayout->addLayout(m_chartsLayout);

    // Lists
    m_listsLayout = new QHBoxLayout();
    m_listsLayout->setSpacing(15);

    QFrame *recentActivityFrame = createRecentActivity();
    QFrame *lowStockFrame = createLowStockAlerts();

    m_listsLayout->addWidget(recentActivityFrame);
    m_listsLayout->addWidget(lowStockFrame);

    m_mainLayout->addLayout(m_listsLayout);
}

QFrame* DashboardWidget::createSummaryCard(const QString &title, const QString &value,
                                         const QString &icon, const QColor &color)
{
    QFrame *card = new QFrame(this);
    card->setObjectName("summaryCard");
    card->setFrameShape(QFrame::NoFrame);

    // Modern gradient background
    QString gradientStyle = QString(
        "QFrame#summaryCard {"
        "    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,"
        "                                stop: 0 %1, stop: 1 %2);"
        "    border-radius: 16px;"
        "    border: 1px solid rgba(255, 255, 255, 0.1);"
        "    padding: 24px;"
        "}"
        "QFrame#summaryCard:hover {"
        "    transform: translateY(-4px);"
        "    border: 1px solid rgba(255, 255, 255, 0.2);"
        "}"
    ).arg(color.name()).arg(color.darker(120).name());

    card->setStyleSheet(gradientStyle);

    // Add modern shadow effect
    QGraphicsDropShadowEffect *shadowEffect = new QGraphicsDropShadowEffect(card);
    shadowEffect->setBlurRadius(20);
    shadowEffect->setColor(QColor(0, 0, 0, 40));
    shadowEffect->setOffset(0, 8);
    card->setGraphicsEffect(shadowEffect);

    QVBoxLayout *cardLayout = new QVBoxLayout(card);
    cardLayout->setSpacing(12);
    cardLayout->setContentsMargins(24, 24, 24, 24);

    // Icon and title row
    QHBoxLayout *headerLayout = new QHBoxLayout();

    // Icon (using emoji for now)
    QLabel *iconLabel = new QLabel("📊", card);
    iconLabel->setStyleSheet("color: rgba(255, 255, 255, 0.9); font-size: 24px;");
    if (title.contains("Sales")) iconLabel->setText("💰");
    else if (title.contains("Purchases")) iconLabel->setText("🛒");
    else if (title.contains("Customers")) iconLabel->setText("👥");
    else if (title.contains("Suppliers")) iconLabel->setText("🏭");

    // Title
    QLabel *titleLabel = new QLabel(title, card);
    titleLabel->setStyleSheet("color: rgba(255, 255, 255, 0.9); font-weight: 600; font-size: 11pt;");

    headerLayout->addWidget(iconLabel);
    headerLayout->addWidget(titleLabel);
    headerLayout->addStretch();

    // Value
    QLabel *valueLabel = new QLabel(value, card);
    valueLabel->setStyleSheet("color: white; font-weight: 700; font-size: 32pt; margin-top: 8px;");

    // Trend indicator (placeholder)
    QLabel *trendLabel = new QLabel("↗ +12%", card);
    trendLabel->setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 9pt; font-weight: 500;");

    // Store reference to value label for updates
    if (title == tr("Total Sales")) {
        m_totalSalesValue = valueLabel;
    } else if (title == tr("Total Purchases")) {
        m_totalPurchasesValue = valueLabel;
    } else if (title == tr("Total Customers")) {
        m_totalCustomersValue = valueLabel;
    } else if (title == tr("Total Suppliers")) {
        m_totalSuppliersValue = valueLabel;
    }

    cardLayout->addLayout(headerLayout);
    cardLayout->addWidget(valueLabel);
    cardLayout->addWidget(trendLabel);
    cardLayout->addStretch();

    return card;
}

QChartView* DashboardWidget::createSalesChart()
{
    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Sales Overview"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create bar series
    QBarSeries *series = new QBarSeries();

    // Sample data - will be replaced with real data
    QBarSet *set0 = new QBarSet(tr("Sales"));
    QBarSet *set1 = new QBarSet(tr("Purchases"));

    *set0 << 1 << 2 << 3 << 4 << 5 << 6;
    *set1 << 5 << 0 << 0 << 4 << 0 << 7;

    series->append(set0);
    series->append(set1);

    chart->addSeries(series);

    // Create axes
    QStringList categories;
    categories << "Jan" << "Feb" << "Mar" << "Apr" << "May" << "Jun";
    QBarCategoryAxis *axisX = new QBarCategoryAxis();
    axisX->append(categories);
    chart->addAxis(axisX, Qt::AlignBottom);
    series->attachAxis(axisX);

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 10);
    chart->addAxis(axisY, Qt::AlignLeft);
    series->attachAxis(axisY);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    return chartView;
}

QChartView* DashboardWidget::createInventoryChart()
{
    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Inventory Status"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create pie series
    QPieSeries *series = new QPieSeries();

    // Sample data - will be replaced with real data
    series->append(tr("Electronics"), 40);
    series->append(tr("Clothing"), 20);
    series->append(tr("Food"), 15);
    series->append(tr("Books"), 10);
    series->append(tr("Other"), 15);

    // Customize slices
    QPieSlice *slice = series->slices().at(0);
    slice->setExploded();
    slice->setLabelVisible();
    slice->setPen(QPen(Qt::darkGreen, 2));
    slice->setBrush(Qt::green);

    chart->addSeries(series);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    return chartView;
}

QFrame* DashboardWidget::createRecentActivity()
{
    QFrame *frame = new QFrame(this);
    frame->setObjectName("activityFrame");
    frame->setFrameShape(QFrame::StyledPanel);
    frame->setFrameShadow(QFrame::Raised);

    m_recentActivityLayout = new QVBoxLayout(frame);

    // Title
    QLabel *titleLabel = new QLabel(tr("Recent Activity"), frame);
    QFont titleFont = titleLabel->font();
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    m_recentActivityLayout->addWidget(titleLabel);

    // Sample data - will be replaced with real data
    QStringList activities = {
        tr("Sale invoice #INV-001 created"),
        tr("New product 'Laptop' added"),
        tr("Payment received from customer 'John Doe'"),
        tr("Purchase invoice #PUR-001 created"),
        tr("Product 'Smartphone' quantity updated")
    };

    for (const QString &activity : activities) {
        QLabel *activityLabel = new QLabel(activity, frame);
        m_recentActivityLayout->addWidget(activityLabel);

        // Add separator line
        QFrame *line = new QFrame(frame);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);
        m_recentActivityLayout->addWidget(line);
    }

    m_recentActivityLayout->addStretch();

    return frame;
}

QFrame* DashboardWidget::createLowStockAlerts()
{
    QFrame *frame = new QFrame(this);
    frame->setObjectName("lowStockFrame");
    frame->setFrameShape(QFrame::StyledPanel);
    frame->setFrameShadow(QFrame::Raised);

    m_lowStockLayout = new QVBoxLayout(frame);

    // Title
    QLabel *titleLabel = new QLabel(tr("Low Stock Alerts"), frame);
    QFont titleFont = titleLabel->font();
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);

    m_lowStockLayout->addWidget(titleLabel);

    // Sample data - will be replaced with real data
    QStringList lowStockItems = {
        tr("Laptop - 2 items left"),
        tr("Smartphone - 3 items left"),
        tr("Headphones - 1 item left"),
        tr("Mouse - 4 items left"),
        tr("Keyboard - 2 items left")
    };

    for (const QString &item : lowStockItems) {
        QLabel *itemLabel = new QLabel(item, frame);
        itemLabel->setStyleSheet("color: red;");
        m_lowStockLayout->addWidget(itemLabel);

        // Add separator line
        QFrame *line = new QFrame(frame);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);
        m_lowStockLayout->addWidget(line);
    }

    m_lowStockLayout->addStretch();

    return frame;
}
