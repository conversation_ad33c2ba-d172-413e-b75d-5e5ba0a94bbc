#include "reportswidget.h"

ReportsWidget::ReportsWidget(QWidget *parent)
    : QWidget(parent),
      m_inventoryService(this),
      m_salesService(this),
      m_purchaseService(this)
{
    createUI();
}

void ReportsWidget::generateReport()
{
    // TODO: Implement report generation based on selected type and date range
    QMessageBox::information(this, tr("Generate Report"),
                           tr("Report generation not implemented yet."));
}

void ReportsWidget::exportReport()
{
    // TODO: Implement report export
    QMessageBox::information(this, tr("Export Report"),
                           tr("Report export not implemented yet."));
}

void ReportsWidget::printReport()
{
    // TODO: Implement report printing
    QMessageBox::information(this, tr("Print Report"),
                           tr("Report printing not implemented yet."));
}

void ReportsWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    m_titleLabel = new QLabel(tr("Reports"), this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(m_titleLabel);

    // Toolbar
    m_toolbarLayout = new QHBoxLayout();
    m_toolbarLayout->setSpacing(10);

    QLabel *reportTypeLabel = new QLabel(tr("Report Type:"), this);
    m_reportTypeCombo = new QComboBox(this);
    m_reportTypeCombo->addItem(tr("Sales Report"));
    m_reportTypeCombo->addItem(tr("Inventory Report"));
    m_reportTypeCombo->addItem(tr("Customer Report"));
    m_reportTypeCombo->addItem(tr("Supplier Report"));

    QLabel *dateRangeLabel = new QLabel(tr("Date Range:"), this);
    m_fromDateEdit = new QDateEdit(QDate::currentDate().addMonths(-1), this);
    m_fromDateEdit->setCalendarPopup(true);
    QLabel *toLabel = new QLabel(tr("to"), this);
    m_toDateEdit = new QDateEdit(QDate::currentDate(), this);
    m_toDateEdit->setCalendarPopup(true);

    m_generateButton = new QPushButton("📊 Generate", this);
    m_generateButton->setProperty("class", "primary");
    connect(m_generateButton, &QPushButton::clicked, this, &ReportsWidget::generateReport);

    m_exportButton = new QPushButton("💾 Export", this);
    connect(m_exportButton, &QPushButton::clicked, this, &ReportsWidget::exportReport);

    m_printButton = new QPushButton("🖨️ Print", this);
    connect(m_printButton, &QPushButton::clicked, this, &ReportsWidget::printReport);

    m_toolbarLayout->addWidget(reportTypeLabel);
    m_toolbarLayout->addWidget(m_reportTypeCombo);
    m_toolbarLayout->addWidget(dateRangeLabel);
    m_toolbarLayout->addWidget(m_fromDateEdit);
    m_toolbarLayout->addWidget(toLabel);
    m_toolbarLayout->addWidget(m_toDateEdit);
    m_toolbarLayout->addWidget(m_generateButton);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_exportButton);
    m_toolbarLayout->addWidget(m_printButton);

    m_mainLayout->addLayout(m_toolbarLayout);

    // Tab widget
    m_tabWidget = new QTabWidget(this);

    // Create report tabs
    m_salesReportTab = createSalesReportTab();
    m_inventoryReportTab = createInventoryReportTab();
    m_customerReportTab = createCustomerReportTab();
    m_supplierReportTab = createSupplierReportTab();

    // Add tabs to tab widget
    m_tabWidget->addTab(m_salesReportTab, tr("Sales"));
    m_tabWidget->addTab(m_inventoryReportTab, tr("Inventory"));
    m_tabWidget->addTab(m_customerReportTab, tr("Customers"));
    m_tabWidget->addTab(m_supplierReportTab, tr("Suppliers"));

    m_mainLayout->addWidget(m_tabWidget);

    // Connect report type combo to tab widget
    connect(m_reportTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            m_tabWidget, &QTabWidget::setCurrentIndex);
}

QWidget* ReportsWidget::createSalesReportTab()
{
    QWidget *tab = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(tab);

    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Sales by Month"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create bar series
    QBarSeries *series = new QBarSeries();

    // Sample data - will be replaced with real data
    QBarSet *set0 = new QBarSet(tr("Sales"));
    QBarSet *set1 = new QBarSet(tr("Purchases"));

    *set0 << 1 << 2 << 3 << 4 << 5 << 6;
    *set1 << 5 << 0 << 0 << 4 << 0 << 7;

    series->append(set0);
    series->append(set1);

    chart->addSeries(series);

    // Create axes
    QStringList categories;
    categories << "Jan" << "Feb" << "Mar" << "Apr" << "May" << "Jun";
    QBarCategoryAxis *axisX = new QBarCategoryAxis();
    axisX->append(categories);
    chart->addAxis(axisX, Qt::AlignBottom);
    series->attachAxis(axisX);

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 10);
    chart->addAxis(axisY, Qt::AlignLeft);
    series->attachAxis(axisY);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    // Create table
    QTableView *tableView = new QTableView(tab);
    tableView->setAlternatingRowColors(true);
    tableView->horizontalHeader()->setStretchLastSection(true);

    // Create model
    QStandardItemModel *model = new QStandardItemModel(6, 3, tab);
    model->setHorizontalHeaderLabels({tr("Month"), tr("Sales"), tr("Purchases")});

    // Sample data - will be replaced with real data
    for (int i = 0; i < 6; ++i) {
        model->setItem(i, 0, new QStandardItem(categories[i]));
        model->setItem(i, 1, new QStandardItem(QString::number(set0->at(i))));
        model->setItem(i, 2, new QStandardItem(QString::number(set1->at(i))));
    }

    tableView->setModel(model);

    // Add widgets to layout
    layout->addWidget(chartView, 2);
    layout->addWidget(tableView, 1);

    return tab;
}

QWidget* ReportsWidget::createInventoryReportTab()
{
    QWidget *tab = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(tab);

    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Inventory by Category"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create pie series
    QPieSeries *series = new QPieSeries();

    // Sample data - will be replaced with real data
    series->append(tr("Electronics"), 40);
    series->append(tr("Clothing"), 20);
    series->append(tr("Food"), 15);
    series->append(tr("Books"), 10);
    series->append(tr("Other"), 15);

    // Customize slices
    QPieSlice *slice = series->slices().at(0);
    slice->setExploded();
    slice->setLabelVisible();
    slice->setPen(QPen(Qt::darkGreen, 2));
    slice->setBrush(Qt::green);

    chart->addSeries(series);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    // Create table
    QTableView *tableView = new QTableView(tab);
    tableView->setAlternatingRowColors(true);
    tableView->horizontalHeader()->setStretchLastSection(true);

    // Create model
    QStandardItemModel *model = new QStandardItemModel(5, 2, tab);
    model->setHorizontalHeaderLabels({tr("Category"), tr("Value")});

    // Sample data - will be replaced with real data
    QStringList categories = {tr("Electronics"), tr("Clothing"), tr("Food"), tr("Books"), tr("Other")};
    QList<qreal> values = {40, 20, 15, 10, 15};

    for (int i = 0; i < 5; ++i) {
        model->setItem(i, 0, new QStandardItem(categories[i]));
        model->setItem(i, 1, new QStandardItem(QString::number(values[i])));
    }

    tableView->setModel(model);

    // Add widgets to layout
    layout->addWidget(chartView, 2);
    layout->addWidget(tableView, 1);

    return tab;
}

QWidget* ReportsWidget::createCustomerReportTab()
{
    QWidget *tab = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(tab);

    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Top Customers"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create bar series
    QBarSeries *series = new QBarSeries();

    // Sample data - will be replaced with real data
    QBarSet *set = new QBarSet(tr("Sales"));

    *set << 5 << 4 << 3 << 2 << 1;

    series->append(set);

    chart->addSeries(series);

    // Create axes
    QStringList categories;
    categories << "Customer A" << "Customer B" << "Customer C" << "Customer D" << "Customer E";
    QBarCategoryAxis *axisX = new QBarCategoryAxis();
    axisX->append(categories);
    chart->addAxis(axisX, Qt::AlignBottom);
    series->attachAxis(axisX);

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 6);
    chart->addAxis(axisY, Qt::AlignLeft);
    series->attachAxis(axisY);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    // Create table
    QTableView *tableView = new QTableView(tab);
    tableView->setAlternatingRowColors(true);
    tableView->horizontalHeader()->setStretchLastSection(true);

    // Create model
    QStandardItemModel *model = new QStandardItemModel(5, 2, tab);
    model->setHorizontalHeaderLabels({tr("Customer"), tr("Sales")});

    // Sample data - will be replaced with real data
    for (int i = 0; i < 5; ++i) {
        model->setItem(i, 0, new QStandardItem(categories[i]));
        model->setItem(i, 1, new QStandardItem(QString::number(set->at(i))));
    }

    tableView->setModel(model);

    // Add widgets to layout
    layout->addWidget(chartView, 2);
    layout->addWidget(tableView, 1);

    return tab;
}

QWidget* ReportsWidget::createSupplierReportTab()
{
    QWidget *tab = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(tab);

    // Create chart
    QChart *chart = new QChart();
    chart->setTitle(tr("Top Suppliers"));
    chart->setAnimationOptions(QChart::SeriesAnimations);

    // Create bar series
    QBarSeries *series = new QBarSeries();

    // Sample data - will be replaced with real data
    QBarSet *set = new QBarSet(tr("Purchases"));

    *set << 5 << 4 << 3 << 2 << 1;

    series->append(set);

    chart->addSeries(series);

    // Create axes
    QStringList categories;
    categories << "Supplier A" << "Supplier B" << "Supplier C" << "Supplier D" << "Supplier E";
    QBarCategoryAxis *axisX = new QBarCategoryAxis();
    axisX->append(categories);
    chart->addAxis(axisX, Qt::AlignBottom);
    series->attachAxis(axisX);

    QValueAxis *axisY = new QValueAxis();
    axisY->setRange(0, 6);
    chart->addAxis(axisY, Qt::AlignLeft);
    series->attachAxis(axisY);

    // Create chart view
    QChartView *chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);

    // Create table
    QTableView *tableView = new QTableView(tab);
    tableView->setAlternatingRowColors(true);
    tableView->horizontalHeader()->setStretchLastSection(true);

    // Create model
    QStandardItemModel *model = new QStandardItemModel(5, 2, tab);
    model->setHorizontalHeaderLabels({tr("Supplier"), tr("Purchases")});

    // Sample data - will be replaced with real data
    for (int i = 0; i < 5; ++i) {
        model->setItem(i, 0, new QStandardItem(categories[i]));
        model->setItem(i, 1, new QStandardItem(QString::number(set->at(i))));
    }

    tableView->setModel(model);

    // Add widgets to layout
    layout->addWidget(chartView, 2);
    layout->addWidget(tableView, 1);

    return tab;
}
