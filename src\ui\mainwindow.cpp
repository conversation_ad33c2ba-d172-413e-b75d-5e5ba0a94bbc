#include "mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      m_darkMode(false)
{
    // Set window properties
    setWindowTitle(tr("Accountant - Inventory & Sales Management"));
    setMinimumSize(1024, 768);

    // Load settings
    loadSettings();

    // Create UI components
    createUI();

    // Show dashboard by default
    showDashboard();

    // Start timer for status bar updates
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    m_timer->start(1000); // Update every second

    // Apply theme
    if (m_darkMode) {
        applyDarkMode();
    } else {
        applyLightMode();
    }
}

MainWindow::~MainWindow()
{
    saveSettings();
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // Ask for confirmation before closing
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, tr("Exit Confirmation"),
        tr("Are you sure you want to exit?"),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        saveSettings();
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::showDashboard()
{
    m_stackedWidget->setCurrentWidget(m_dashboardWidget);
    m_dashboardButton->setChecked(true);
    m_inventoryButton->setChecked(false);
    m_salesButton->setChecked(false);
    m_customersButton->setChecked(false);
    m_suppliersButton->setChecked(false);
    m_reportsButton->setChecked(false);
    m_statusLabel->setText(tr("Dashboard"));
}

void MainWindow::showInventory()
{
    m_stackedWidget->setCurrentWidget(m_inventoryWidget);
    m_dashboardButton->setChecked(false);
    m_inventoryButton->setChecked(true);
    m_salesButton->setChecked(false);
    m_customersButton->setChecked(false);
    m_suppliersButton->setChecked(false);
    m_reportsButton->setChecked(false);
    m_statusLabel->setText(tr("Inventory Management"));
}

void MainWindow::showSales()
{
    m_stackedWidget->setCurrentWidget(m_salesWidget);
    m_dashboardButton->setChecked(false);
    m_inventoryButton->setChecked(false);
    m_salesButton->setChecked(true);
    m_customersButton->setChecked(false);
    m_suppliersButton->setChecked(false);
    m_reportsButton->setChecked(false);
    m_statusLabel->setText(tr("Sales Management"));
}

void MainWindow::showCustomers()
{
    m_stackedWidget->setCurrentWidget(m_customersWidget);
    m_dashboardButton->setChecked(false);
    m_inventoryButton->setChecked(false);
    m_salesButton->setChecked(false);
    m_customersButton->setChecked(true);
    m_suppliersButton->setChecked(false);
    m_reportsButton->setChecked(false);
    m_statusLabel->setText(tr("Customer Management"));
}

void MainWindow::showSuppliers()
{
    m_stackedWidget->setCurrentWidget(m_suppliersWidget);
    m_dashboardButton->setChecked(false);
    m_inventoryButton->setChecked(false);
    m_salesButton->setChecked(false);
    m_customersButton->setChecked(false);
    m_suppliersButton->setChecked(true);
    m_reportsButton->setChecked(false);
    m_statusLabel->setText(tr("Supplier Management"));
}

void MainWindow::showReports()
{
    m_stackedWidget->setCurrentWidget(m_reportsWidget);
    m_dashboardButton->setChecked(false);
    m_inventoryButton->setChecked(false);
    m_salesButton->setChecked(false);
    m_customersButton->setChecked(false);
    m_suppliersButton->setChecked(false);
    m_reportsButton->setChecked(true);
    m_statusLabel->setText(tr("Reports"));
}

void MainWindow::showAboutDialog()
{
    QMessageBox::about(this, tr("About Accountant"),
                      tr("<h2>Accountant</h2>"
                         "<p>Version 0.1</p>"
                         "<p>Inventory & Sales Management System</p>"
                         "<p>&copy; 2023 Your Company</p>"));
}

void MainWindow::showSettingsDialog()
{
    // TODO: Implement settings dialog
    QMessageBox::information(this, tr("Settings"),
                           tr("Settings dialog not implemented yet."));
}

void MainWindow::updateStatusBar()
{
    m_timeLabel->setText(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
}

void MainWindow::toggleDarkMode()
{
    m_darkMode = !m_darkMode;

    if (m_darkMode) {
        applyDarkMode();
    } else {
        applyLightMode();
    }

    m_darkModeAction->setChecked(m_darkMode);
}

void MainWindow::createUI()
{
    createMenuBar();
    createToolBar();
    createStatusBar();
    createCentralWidget();
    createSideBar();
}

void MainWindow::createMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu(tr("&File"));

    m_exitAction = new QAction(tr("E&xit"), this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    connect(m_exitAction, &QAction::triggered, this, &QMainWindow::close);
    fileMenu->addAction(m_exitAction);

    // View menu
    QMenu *viewMenu = menuBar()->addMenu(tr("&View"));

    m_darkModeAction = new QAction(tr("&Dark Mode"), this);
    m_darkModeAction->setCheckable(true);
    m_darkModeAction->setChecked(m_darkMode);
    connect(m_darkModeAction, &QAction::triggered, this, &MainWindow::toggleDarkMode);
    viewMenu->addAction(m_darkModeAction);

    // Tools menu
    QMenu *toolsMenu = menuBar()->addMenu(tr("&Tools"));

    m_settingsAction = new QAction(tr("&Settings"), this);
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::showSettingsDialog);
    toolsMenu->addAction(m_settingsAction);

    // Help menu
    QMenu *helpMenu = menuBar()->addMenu(tr("&Help"));

    m_aboutAction = new QAction(tr("&About"), this);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAboutDialog);
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::createToolBar()
{
    QToolBar *toolBar = addToolBar(tr("Main Toolbar"));
    toolBar->setMovable(false);
    toolBar->setIconSize(QSize(24, 24));

    toolBar->addAction(m_exitAction);
    toolBar->addSeparator();
    toolBar->addAction(m_settingsAction);
    toolBar->addSeparator();
    toolBar->addAction(m_aboutAction);
}

void MainWindow::createStatusBar()
{
    m_statusLabel = new QLabel(tr("Ready"));
    statusBar()->addWidget(m_statusLabel, 1);

    m_timeLabel = new QLabel(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    statusBar()->addPermanentWidget(m_timeLabel);
}

void MainWindow::createCentralWidget()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);

    // Create stacked widget for pages
    m_stackedWidget = new QStackedWidget(this);

    // Create page widgets
    m_dashboardWidget = new DashboardWidget(this);
    m_inventoryWidget = new InventoryWidget(this);
    m_salesWidget = new SalesWidget(this);
    m_customersWidget = new CustomersWidget(this);
    m_suppliersWidget = new SuppliersWidget(this);
    m_reportsWidget = new ReportsWidget(this);

    // Add pages to stacked widget
    m_stackedWidget->addWidget(m_dashboardWidget);
    m_stackedWidget->addWidget(m_inventoryWidget);
    m_stackedWidget->addWidget(m_salesWidget);
    m_stackedWidget->addWidget(m_customersWidget);
    m_stackedWidget->addWidget(m_suppliersWidget);
    m_stackedWidget->addWidget(m_reportsWidget);
}

void MainWindow::createSideBar()
{
    // Create sidebar frame
    m_sideBar = new QFrame(this);
    m_sideBar->setObjectName("sideBar");
    m_sideBar->setMinimumWidth(200);
    m_sideBar->setMaximumWidth(200);
    m_sideBarLayout = new QVBoxLayout(m_sideBar);
    m_sideBarLayout->setSpacing(10);
    m_sideBarLayout->setContentsMargins(10, 20, 10, 20);

    // Create app logo/title with modern styling
    QLabel *logoLabel = new QLabel("📊 Accountant", this);
    logoLabel->setObjectName("logoLabel");
    QFont logoFont = logoLabel->font();
    logoFont.setPointSize(18);
    logoFont.setBold(true);
    logoFont.setFamily("Segoe UI");
    logoLabel->setFont(logoFont);
    logoLabel->setAlignment(Qt::AlignCenter);
    logoLabel->setStyleSheet("color: #58a6ff; padding: 20px 0px; font-weight: 700;");
    m_sideBarLayout->addWidget(logoLabel);

    // Add spacer
    m_sideBarLayout->addSpacing(20);

    // Create sidebar buttons with modern styling and icons
    m_dashboardButton = new QToolButton(this);
    m_dashboardButton->setText("  Dashboard");
    m_dashboardButton->setIcon(IconHelper::getDashboardIcon());
    m_dashboardButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_dashboardButton->setCheckable(true);
    m_dashboardButton->setAutoRaise(true);
    m_dashboardButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_dashboardButton->setMinimumHeight(48);
    m_dashboardButton->setIconSize(QSize(20, 20));
    connect(m_dashboardButton, &QToolButton::clicked, this, &MainWindow::showDashboard);
    m_sideBarLayout->addWidget(m_dashboardButton);

    m_inventoryButton = new QToolButton(this);
    m_inventoryButton->setText("  Inventory");
    m_inventoryButton->setIcon(IconHelper::getInventoryIcon());
    m_inventoryButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_inventoryButton->setCheckable(true);
    m_inventoryButton->setAutoRaise(true);
    m_inventoryButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_inventoryButton->setMinimumHeight(48);
    m_inventoryButton->setIconSize(QSize(20, 20));
    connect(m_inventoryButton, &QToolButton::clicked, this, &MainWindow::showInventory);
    m_sideBarLayout->addWidget(m_inventoryButton);

    m_salesButton = new QToolButton(this);
    m_salesButton->setText("  Sales");
    m_salesButton->setIcon(IconHelper::getSalesIcon());
    m_salesButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_salesButton->setCheckable(true);
    m_salesButton->setAutoRaise(true);
    m_salesButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_salesButton->setMinimumHeight(48);
    m_salesButton->setIconSize(QSize(20, 20));
    connect(m_salesButton, &QToolButton::clicked, this, &MainWindow::showSales);
    m_sideBarLayout->addWidget(m_salesButton);

    m_customersButton = new QToolButton(this);
    m_customersButton->setText("  Customers");
    m_customersButton->setIcon(IconHelper::getCustomersIcon());
    m_customersButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_customersButton->setCheckable(true);
    m_customersButton->setAutoRaise(true);
    m_customersButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_customersButton->setMinimumHeight(48);
    m_customersButton->setIconSize(QSize(20, 20));
    connect(m_customersButton, &QToolButton::clicked, this, &MainWindow::showCustomers);
    m_sideBarLayout->addWidget(m_customersButton);

    m_suppliersButton = new QToolButton(this);
    m_suppliersButton->setText("  Suppliers");
    m_suppliersButton->setIcon(IconHelper::getSuppliersIcon());
    m_suppliersButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_suppliersButton->setCheckable(true);
    m_suppliersButton->setAutoRaise(true);
    m_suppliersButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_suppliersButton->setMinimumHeight(48);
    m_suppliersButton->setIconSize(QSize(20, 20));
    connect(m_suppliersButton, &QToolButton::clicked, this, &MainWindow::showSuppliers);
    m_sideBarLayout->addWidget(m_suppliersButton);

    m_reportsButton = new QToolButton(this);
    m_reportsButton->setText("  Reports");
    m_reportsButton->setIcon(IconHelper::getReportsIcon());
    m_reportsButton->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    m_reportsButton->setCheckable(true);
    m_reportsButton->setAutoRaise(true);
    m_reportsButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    m_reportsButton->setMinimumHeight(48);
    m_reportsButton->setIconSize(QSize(20, 20));
    connect(m_reportsButton, &QToolButton::clicked, this, &MainWindow::showReports);
    m_sideBarLayout->addWidget(m_reportsButton);

    // Add spacer at the bottom
    m_sideBarLayout->addStretch();

    // Add sidebar and stacked widget to main layout
    m_mainLayout->addWidget(m_sideBar);
    m_mainLayout->addWidget(m_stackedWidget);
}

void MainWindow::loadSettings()
{
    QSettings settings;

    // Load window geometry
    restoreGeometry(settings.value("mainWindow/geometry").toByteArray());
    restoreState(settings.value("mainWindow/windowState").toByteArray());

    // Load dark mode setting
    m_darkMode = settings.value("appearance/darkMode", false).toBool();
}

void MainWindow::saveSettings()
{
    QSettings settings;

    // Save window geometry
    settings.setValue("mainWindow/geometry", saveGeometry());
    settings.setValue("mainWindow/windowState", saveState());

    // Save dark mode setting
    settings.setValue("appearance/darkMode", m_darkMode);
}

void MainWindow::applyDarkMode()
{
    // Apply dark mode stylesheet
    QFile styleFile(":/styles/dark.qss");
    if (styleFile.open(QFile::ReadOnly | QFile::Text)) {
        QTextStream stream(&styleFile);
        qApp->setStyleSheet(stream.readAll());
        styleFile.close();
    } else {
        // Fallback dark style
        QPalette darkPalette;
        darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
        darkPalette.setColor(QPalette::WindowText, Qt::white);
        darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
        darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
        darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
        darkPalette.setColor(QPalette::ToolTipText, Qt::white);
        darkPalette.setColor(QPalette::Text, Qt::white);
        darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
        darkPalette.setColor(QPalette::ButtonText, Qt::white);
        darkPalette.setColor(QPalette::BrightText, Qt::red);
        darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
        darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
        darkPalette.setColor(QPalette::HighlightedText, Qt::black);
        qApp->setPalette(darkPalette);
    }
}

void MainWindow::applyLightMode()
{
    // Apply light mode stylesheet
    QFile styleFile(":/styles/light.qss");
    if (styleFile.open(QFile::ReadOnly | QFile::Text)) {
        QTextStream stream(&styleFile);
        qApp->setStyleSheet(stream.readAll());
        styleFile.close();
    } else {
        // Fallback light style
        qApp->setStyle(QStyleFactory::create("Fusion"));
        qApp->setPalette(QApplication::palette());
        qApp->setStyleSheet("");
    }
}
