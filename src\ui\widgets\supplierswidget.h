#ifndef SUPPLIERSWIDGET_H
#define SUPPLIERSWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTableView>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QSortFilterProxyModel>
#include <QMessageBox>
#include <QDialog>
#include <QFormLayout>
#include <QDoubleSpinBox>
#include <QDialogButtonBox>
#include <QDebug>

#include "../../core/services/purchaseservice.h"
#include "../../core/models/supplier.h"

/**
 * @brief The SuppliersWidget class displays and manages suppliers
 */
class SuppliersWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SuppliersWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Load suppliers from the database
     */
    void loadSuppliers();

    /**
     * @brief Add a new supplier
     */
    void addSupplier();

    /**
     * @brief Edit the selected supplier
     */
    void editSupplier();

    /**
     * @brief Delete the selected supplier
     */
    void deleteSupplier();

    /**
     * @brief Filter suppliers based on search text
     */
    void filterSuppliers();

    /**
     * @brief Handle double-click on a supplier to edit it
     * @param index The model index that was double-clicked
     */
    void onSupplierDoubleClicked(const QModelIndex &index);

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create the supplier dialog for adding/editing
     * @param supplier Supplier to edit, or null for a new supplier
     * @return true if the dialog was accepted, false otherwise
     */
    bool showSupplierDialog(QSharedPointer<Supplier> supplier = nullptr);

    /**
     * @brief Update the supplier model with the current suppliers
     */
    void updateSupplierModel();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolbarLayout;
    QHBoxLayout *m_searchLayout;

    // Toolbar widgets
    QLabel *m_titleLabel;
    QPushButton *m_addButton;
    QPushButton *m_editButton;
    QPushButton *m_deleteButton;
    QPushButton *m_refreshButton;

    // Search widgets
    QLineEdit *m_searchEdit;
    QPushButton *m_searchButton;

    // Table view
    QTableView *m_suppliersTable;
    QStandardItemModel *m_suppliersModel;
    QSortFilterProxyModel *m_proxyModel;

    // Service
    PurchaseService m_purchaseService;

    // Current suppliers
    QList<QSharedPointer<Supplier>> m_suppliers;
};

#endif // SUPPLIERSWIDGET_H
