#include "customerswidget.h"

CustomersWidget::CustomersWidget(QWidget *parent)
    : QWidget(parent),
      m_salesService(this)
{
    createUI();
    loadCustomers();
}

void CustomersWidget::loadCustomers()
{
    // Get customers from service
    m_customers = m_salesService.getAllCustomers();

    // Update the model
    updateCustomerModel();
}

void CustomersWidget::addCustomer()
{
    if (showCustomerDialog()) {
        loadCustomers();
    }
}

void CustomersWidget::editCustomer()
{
    // Get the selected customer
    QModelIndex currentIndex = m_customersTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a customer to edit."));
        return;
    }

    // Get the customer ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int customerId = m_customersModel->data(m_customersModel->index(row, 0), Qt::UserRole).toInt();

    // Find the customer in the list
    QSharedPointer<Customer> customer;
    for (const QSharedPointer<Customer> &c : m_customers) {
        if (c->id() == customerId) {
            customer = c;
            break;
        }
    }

    if (!customer) {
        QMessageBox::warning(this, tr("Error"), tr("Customer not found."));
        return;
    }

    // Show the edit dialog
    if (showCustomerDialog(customer)) {
        loadCustomers();
    }
}

void CustomersWidget::deleteCustomer()
{
    // Get the selected customer
    QModelIndex currentIndex = m_customersTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a customer to delete."));
        return;
    }

    // Get the customer ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int customerId = m_customersModel->data(m_customersModel->index(row, 0), Qt::UserRole).toInt();
    QString customerName = m_customersModel->data(m_customersModel->index(row, 1)).toString();

    // Confirm deletion
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, tr("Confirm Deletion"),
        tr("Are you sure you want to delete the customer '%1'?").arg(customerName),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // Delete the customer
        if (m_salesService.deleteCustomer(customerId)) {
            loadCustomers();
        } else {
            QMessageBox::critical(this, tr("Error"), tr("Failed to delete the customer. The customer may have associated invoices."));
        }
    }
}

void CustomersWidget::filterCustomers()
{
    QString searchText = m_searchEdit->text();

    // Set the filter for the proxy model
    m_proxyModel->setFilterFixedString(searchText);
}

void CustomersWidget::onCustomerDoubleClicked(const QModelIndex &index)
{
    if (index.isValid()) {
        editCustomer();
    }
}

void CustomersWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    m_titleLabel = new QLabel(tr("Customer Management"), this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(m_titleLabel);

    // Toolbar
    m_toolbarLayout = new QHBoxLayout();
    m_toolbarLayout->setSpacing(10);

    m_addButton = new QPushButton("👤 Add Customer", this);
    m_addButton->setProperty("class", "primary");
    connect(m_addButton, &QPushButton::clicked, this, &CustomersWidget::addCustomer);

    m_editButton = new QPushButton("✏️ Edit", this);
    m_editButton->setEnabled(false);
    connect(m_editButton, &QPushButton::clicked, this, &CustomersWidget::editCustomer);

    m_deleteButton = new QPushButton("🗑️ Delete", this);
    m_deleteButton->setProperty("class", "danger");
    m_deleteButton->setEnabled(false);
    connect(m_deleteButton, &QPushButton::clicked, this, &CustomersWidget::deleteCustomer);

    m_refreshButton = new QPushButton("🔄 Refresh", this);
    connect(m_refreshButton, &QPushButton::clicked, this, &CustomersWidget::loadCustomers);

    m_toolbarLayout->addWidget(m_addButton);
    m_toolbarLayout->addWidget(m_editButton);
    m_toolbarLayout->addWidget(m_deleteButton);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_refreshButton);

    m_mainLayout->addLayout(m_toolbarLayout);

    // Search bar
    m_searchLayout = new QHBoxLayout();
    m_searchLayout->setSpacing(10);

    QLabel *searchLabel = new QLabel(tr("Search:"), this);
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Enter customer name or phone"));
    connect(m_searchEdit, &QLineEdit::textChanged, this, &CustomersWidget::filterCustomers);

    m_searchButton = new QPushButton(tr("Search"), this);
    m_searchButton->setIcon(QIcon::fromTheme("system-search", QIcon(":/icons/search.png")));
    connect(m_searchButton, &QPushButton::clicked, this, &CustomersWidget::filterCustomers);

    m_searchLayout->addWidget(searchLabel);
    m_searchLayout->addWidget(m_searchEdit, 1);
    m_searchLayout->addWidget(m_searchButton);

    m_mainLayout->addLayout(m_searchLayout);

    // Customers table
    m_customersTable = new QTableView(this);
    m_customersTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_customersTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_customersTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_customersTable->setAlternatingRowColors(true);
    m_customersTable->setSortingEnabled(true);
    m_customersTable->horizontalHeader()->setStretchLastSection(true);
    m_customersTable->verticalHeader()->setVisible(false);

    // Create the model
    m_customersModel = new QStandardItemModel(0, 5, this);
    m_customersModel->setHorizontalHeaderLabels({
        tr("ID"), tr("Name"), tr("Phone"), tr("Email"), tr("Balance")
    });

    // Create the proxy model for filtering and sorting
    m_proxyModel = new QSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_customersModel);
    m_proxyModel->setFilterKeyColumn(1); // Filter by name
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);

    m_customersTable->setModel(m_proxyModel);

    // Hide the ID column
    m_customersTable->hideColumn(0);

    // Connect signals
    connect(m_customersTable, &QTableView::doubleClicked,
            this, &CustomersWidget::onCustomerDoubleClicked);
    connect(m_customersTable->selectionModel(), &QItemSelectionModel::selectionChanged,
            [this]() {
                bool hasSelection = m_customersTable->selectionModel()->hasSelection();
                m_editButton->setEnabled(hasSelection);
                m_deleteButton->setEnabled(hasSelection);
            });

    m_mainLayout->addWidget(m_customersTable);
}

bool CustomersWidget::showCustomerDialog(QSharedPointer<Customer> customer)
{
    // Create a new customer if none was provided
    bool isNewCustomer = !customer;
    if (isNewCustomer) {
        customer = QSharedPointer<Customer>(new Customer());
    }

    // Create the dialog
    QDialog dialog(this);
    dialog.setWindowTitle(isNewCustomer ? tr("Add Customer") : tr("Edit Customer"));
    dialog.setMinimumWidth(400);

    QVBoxLayout *dialogLayout = new QVBoxLayout(&dialog);

    // Form layout
    QFormLayout *formLayout = new QFormLayout();

    // Name
    QLineEdit *nameEdit = new QLineEdit(&dialog);
    nameEdit->setText(customer->name());
    formLayout->addRow(tr("Name:"), nameEdit);

    // Phone
    QLineEdit *phoneEdit = new QLineEdit(&dialog);
    phoneEdit->setText(customer->phone());
    formLayout->addRow(tr("Phone:"), phoneEdit);

    // Email
    QLineEdit *emailEdit = new QLineEdit(&dialog);
    emailEdit->setText(customer->email());
    formLayout->addRow(tr("Email:"), emailEdit);

    // Address
    QLineEdit *addressEdit = new QLineEdit(&dialog);
    addressEdit->setText(customer->address());
    formLayout->addRow(tr("Address:"), addressEdit);

    // Balance
    QDoubleSpinBox *balanceSpinBox = new QDoubleSpinBox(&dialog);
    balanceSpinBox->setRange(-1000000, 1000000);
    balanceSpinBox->setDecimals(2);
    balanceSpinBox->setValue(customer->balance());
    balanceSpinBox->setEnabled(!isNewCustomer); // Only allow editing balance for existing customers
    formLayout->addRow(tr("Balance:"), balanceSpinBox);

    dialogLayout->addLayout(formLayout);

    // Buttons
    QDialogButtonBox *buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel,
        Qt::Horizontal, &dialog);
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    dialogLayout->addWidget(buttonBox);

    // Show the dialog
    if (dialog.exec() == QDialog::Accepted) {
        // Update the customer with the new values
        customer->setName(nameEdit->text());
        customer->setPhone(phoneEdit->text());
        customer->setEmail(emailEdit->text());
        customer->setAddress(addressEdit->text());
        customer->setBalance(balanceSpinBox->value());

        // Save the customer
        bool success;
        if (isNewCustomer) {
            success = m_salesService.addCustomer(customer);
        } else {
            success = m_salesService.updateCustomer(customer);
        }

        if (!success) {
            QMessageBox::critical(this, tr("Error"),
                                 tr("Failed to save the customer."));
            return false;
        }

        return true;
    }

    return false;
}

void CustomersWidget::updateCustomerModel()
{
    // Clear the model
    m_customersModel->removeRows(0, m_customersModel->rowCount());

    // Add customers to the model
    for (const QSharedPointer<Customer> &customer : m_customers) {
        QList<QStandardItem*> row;

        // ID (hidden, used for reference)
        QStandardItem *idItem = new QStandardItem(QString::number(customer->id()));
        idItem->setData(customer->id(), Qt::UserRole);
        row.append(idItem);

        // Name
        row.append(new QStandardItem(customer->name()));

        // Phone
        row.append(new QStandardItem(customer->phone()));

        // Email
        row.append(new QStandardItem(customer->email()));

        // Balance
        QStandardItem *balanceItem = new QStandardItem(QString::number(customer->balance(), 'f', 2));
        if (customer->hasDebt()) {
            balanceItem->setForeground(Qt::red);
        }
        row.append(balanceItem);

        m_customersModel->appendRow(row);
    }

    // Resize columns to contents
    m_customersTable->resizeColumnsToContents();
}
