using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Employee : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private string _phone;
        private string _email;
        private string _address;
        private string _position;
        private decimal _salary;
        private DateTime _hireDate;
        private string _status; // Active, Inactive, On Leave, etc.
        private DateTime _createdAt;
        private DateTime? _updatedAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Address
        {
            get => _address;
            set
            {
                if (_address != value)
                {
                    _address = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Position
        {
            get => _position;
            set
            {
                if (_position != value)
                {
                    _position = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Salary
        {
            get => _salary;
            set
            {
                if (_salary != value)
                {
                    _salary = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime HireDate
        {
            get => _hireDate;
            set
            {
                if (_hireDate != value)
                {
                    _hireDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(YearsOfService));
                }
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public int YearsOfService => (DateTime.Now - HireDate).Days / 365;

        public bool IsActive => Status?.ToLower() == "active";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class EmployeeValidator : AbstractValidator<Employee>
    {
        public EmployeeValidator()
        {
            RuleFor(e => e.Name).NotEmpty().WithMessage("Employee name is required")
                .MaximumLength(100).WithMessage("Employee name cannot exceed 100 characters");

            RuleFor(e => e.Phone).MaximumLength(20).WithMessage("Phone number cannot exceed 20 characters");

            RuleFor(e => e.Email).EmailAddress().When(e => !string.IsNullOrEmpty(e.Email))
                .WithMessage("Invalid email address format")
                .MaximumLength(100).WithMessage("Email cannot exceed 100 characters");

            RuleFor(e => e.Address).MaximumLength(200).WithMessage("Address cannot exceed 200 characters");

            RuleFor(e => e.Position).NotEmpty().WithMessage("Position is required")
                .MaximumLength(50).WithMessage("Position cannot exceed 50 characters");

            RuleFor(e => e.Salary).GreaterThan(0).WithMessage("Salary must be greater than 0");

            RuleFor(e => e.HireDate).NotEmpty().WithMessage("Hire date is required")
                .LessThanOrEqualTo(DateTime.Now).WithMessage("Hire date cannot be in the future");

            RuleFor(e => e.Status).NotEmpty().WithMessage("Status is required")
                .Must(s => new[] { "active", "inactive", "on leave" }.Contains(s.ToLower()))
                .WithMessage("Status must be one of: Active, Inactive, On Leave");
        }
    }
}