#include "inventorywidget.h"

InventoryWidget::InventoryWidget(QWidget *parent)
    : QWidget(parent),
      m_inventoryService(this)
{
    createUI();
    loadProducts();
}

void InventoryWidget::loadProducts()
{
    // Get products from service
    m_products = m_inventoryService.getAllProducts();

    // Update the model
    updateProductModel();
}

void InventoryWidget::addProduct()
{
    if (showProductDialog()) {
        loadProducts();
    }
}

void InventoryWidget::editProduct()
{
    // Get the selected product
    QModelIndex currentIndex = m_productsTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a product to edit."));
        return;
    }

    // Get the product ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int productId = m_productsModel->data(m_productsModel->index(row, 0), Qt::UserRole).toInt();

    // Find the product in the list
    QSharedPointer<Product> product;
    for (const QSharedPointer<Product> &p : m_products) {
        if (p->id() == productId) {
            product = p;
            break;
        }
    }

    if (!product) {
        QMessageBox::warning(this, tr("Error"), tr("Product not found."));
        return;
    }

    // Show the edit dialog
    if (showProductDialog(product)) {
        loadProducts();
    }
}

void InventoryWidget::deleteProduct()
{
    // Get the selected product
    QModelIndex currentIndex = m_productsTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a product to delete."));
        return;
    }

    // Get the product ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int productId = m_productsModel->data(m_productsModel->index(row, 0), Qt::UserRole).toInt();
    QString productName = m_productsModel->data(m_productsModel->index(row, 1)).toString();

    // Confirm deletion
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, tr("Confirm Deletion"),
        tr("Are you sure you want to delete the product '%1'?").arg(productName),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // Delete the product
        if (m_inventoryService.deleteProduct(productId)) {
            loadProducts();
        } else {
            QMessageBox::critical(this, tr("Error"), tr("Failed to delete the product."));
        }
    }
}

void InventoryWidget::filterProducts()
{
    QString searchText = m_searchEdit->text();
    QString category = m_categoryCombo->currentText();

    // If "All Categories" is selected, don't filter by category
    if (category == tr("All Categories")) {
        category = "";
    }

    // Set the filter for the proxy model
    m_proxyModel->setFilterFixedString(searchText);

    // TODO: Implement category filtering
}

void InventoryWidget::onProductDoubleClicked(const QModelIndex &index)
{
    if (index.isValid()) {
        editProduct();
    }
}

void InventoryWidget::onProductSelectionChanged()
{
    bool hasSelection = m_productsTable->selectionModel()->hasSelection();
    m_editButton->setEnabled(hasSelection);
    m_deleteButton->setEnabled(hasSelection);
}

void InventoryWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    m_titleLabel = new QLabel(tr("Inventory Management"), this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(m_titleLabel);

    // Toolbar
    m_toolbarLayout = new QHBoxLayout();
    m_toolbarLayout->setSpacing(10);

    m_addButton = new QPushButton(" Add Product", this);
    m_addButton->setIcon(IconHelper::getAddIcon());
    m_addButton->setProperty("class", "primary");
    connect(m_addButton, &QPushButton::clicked, this, &InventoryWidget::addProduct);

    m_editButton = new QPushButton(" Edit", this);
    m_editButton->setIcon(IconHelper::getEditIcon());
    m_editButton->setEnabled(false);
    connect(m_editButton, &QPushButton::clicked, this, &InventoryWidget::editProduct);

    m_deleteButton = new QPushButton(" Delete", this);
    m_deleteButton->setIcon(IconHelper::getDeleteIcon());
    m_deleteButton->setProperty("class", "danger");
    m_deleteButton->setEnabled(false);
    connect(m_deleteButton, &QPushButton::clicked, this, &InventoryWidget::deleteProduct);

    m_refreshButton = new QPushButton(" Refresh", this);
    m_refreshButton->setIcon(IconHelper::getRefreshIcon());
    connect(m_refreshButton, &QPushButton::clicked, this, &InventoryWidget::loadProducts);

    m_toolbarLayout->addWidget(m_addButton);
    m_toolbarLayout->addWidget(m_editButton);
    m_toolbarLayout->addWidget(m_deleteButton);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_refreshButton);

    m_mainLayout->addLayout(m_toolbarLayout);

    // Search bar
    m_searchLayout = new QHBoxLayout();
    m_searchLayout->setSpacing(10);

    QLabel *searchLabel = new QLabel(tr("Search:"), this);
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Enter product name or code"));
    connect(m_searchEdit, &QLineEdit::textChanged, this, &InventoryWidget::filterProducts);

    QLabel *categoryLabel = new QLabel(tr("Category:"), this);
    m_categoryCombo = new QComboBox(this);
    m_categoryCombo->addItem(tr("All Categories"));
    // TODO: Load categories from database
    m_categoryCombo->addItem(tr("Electronics"));
    m_categoryCombo->addItem(tr("Clothing"));
    m_categoryCombo->addItem(tr("Food"));
    m_categoryCombo->addItem(tr("Books"));
    connect(m_categoryCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &InventoryWidget::filterProducts);

    m_searchButton = new QPushButton(tr("Search"), this);
    m_searchButton->setIcon(QIcon::fromTheme("system-search", QIcon(":/icons/search.png")));
    connect(m_searchButton, &QPushButton::clicked, this, &InventoryWidget::filterProducts);

    m_searchLayout->addWidget(searchLabel);
    m_searchLayout->addWidget(m_searchEdit, 1);
    m_searchLayout->addWidget(categoryLabel);
    m_searchLayout->addWidget(m_categoryCombo);
    m_searchLayout->addWidget(m_searchButton);

    m_mainLayout->addLayout(m_searchLayout);

    // Products table
    m_productsTable = new QTableView(this);
    m_productsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_productsTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_productsTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_productsTable->setAlternatingRowColors(true);
    m_productsTable->setSortingEnabled(true);
    m_productsTable->horizontalHeader()->setStretchLastSection(true);
    m_productsTable->verticalHeader()->setVisible(false);

    // Create the model
    m_productsModel = new QStandardItemModel(0, 7, this);
    m_productsModel->setHorizontalHeaderLabels({
        tr("ID"), tr("Code"), tr("Name"), tr("Category"),
        tr("Purchase Price"), tr("Selling Price"), tr("Quantity")
    });

    // Create the proxy model for filtering and sorting
    m_proxyModel = new QSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_productsModel);
    m_proxyModel->setFilterKeyColumn(2); // Filter by name
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);

    m_productsTable->setModel(m_proxyModel);

    // Hide the ID column
    m_productsTable->hideColumn(0);

    // Connect signals
    connect(m_productsTable, &QTableView::doubleClicked,
            this, &InventoryWidget::onProductDoubleClicked);
    connect(m_productsTable->selectionModel(), &QItemSelectionModel::selectionChanged,
            this, &InventoryWidget::onProductSelectionChanged);

    m_mainLayout->addWidget(m_productsTable);
}

bool InventoryWidget::showProductDialog(QSharedPointer<Product> product)
{
    // Create a new product if none was provided
    bool isNewProduct = !product;
    if (isNewProduct) {
        product = QSharedPointer<Product>(new Product());
    }

    // Create the dialog
    QDialog dialog(this);
    dialog.setWindowTitle(isNewProduct ? tr("Add Product") : tr("Edit Product"));
    dialog.setMinimumWidth(400);

    QVBoxLayout *dialogLayout = new QVBoxLayout(&dialog);

    // Form layout
    QFormLayout *formLayout = new QFormLayout();

    // Code
    QLineEdit *codeEdit = new QLineEdit(&dialog);
    codeEdit->setText(product->code());
    formLayout->addRow(tr("Code:"), codeEdit);

    // Name
    QLineEdit *nameEdit = new QLineEdit(&dialog);
    nameEdit->setText(product->name());
    formLayout->addRow(tr("Name:"), nameEdit);

    // Description
    QLineEdit *descriptionEdit = new QLineEdit(&dialog);
    descriptionEdit->setText(product->description());
    formLayout->addRow(tr("Description:"), descriptionEdit);

    // Category
    QComboBox *categoryCombo = new QComboBox(&dialog);
    categoryCombo->setEditable(true);
    categoryCombo->addItems({"Electronics", "Clothing", "Food", "Books", "Other"});
    categoryCombo->setCurrentText(product->category());
    formLayout->addRow(tr("Category:"), categoryCombo);

    // Purchase Price
    QDoubleSpinBox *purchasePriceSpinBox = new QDoubleSpinBox(&dialog);
    purchasePriceSpinBox->setRange(0, 1000000);
    purchasePriceSpinBox->setDecimals(2);
    purchasePriceSpinBox->setValue(product->purchasePrice());
    formLayout->addRow(tr("Purchase Price:"), purchasePriceSpinBox);

    // Selling Price
    QDoubleSpinBox *sellingPriceSpinBox = new QDoubleSpinBox(&dialog);
    sellingPriceSpinBox->setRange(0, 1000000);
    sellingPriceSpinBox->setDecimals(2);
    sellingPriceSpinBox->setValue(product->sellingPrice());
    formLayout->addRow(tr("Selling Price:"), sellingPriceSpinBox);

    // Quantity
    QSpinBox *quantitySpinBox = new QSpinBox(&dialog);
    quantitySpinBox->setRange(0, 1000000);
    quantitySpinBox->setValue(product->quantity());
    formLayout->addRow(tr("Quantity:"), quantitySpinBox);

    // Min Quantity
    QSpinBox *minQuantitySpinBox = new QSpinBox(&dialog);
    minQuantitySpinBox->setRange(0, 1000000);
    minQuantitySpinBox->setValue(product->minQuantity());
    formLayout->addRow(tr("Min Quantity:"), minQuantitySpinBox);

    dialogLayout->addLayout(formLayout);

    // Buttons
    QDialogButtonBox *buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel,
        Qt::Horizontal, &dialog);
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    dialogLayout->addWidget(buttonBox);

    // Show the dialog
    if (dialog.exec() == QDialog::Accepted) {
        // Update the product with the new values
        product->setCode(codeEdit->text());
        product->setName(nameEdit->text());
        product->setDescription(descriptionEdit->text());
        product->setCategory(categoryCombo->currentText());
        product->setPurchasePrice(purchasePriceSpinBox->value());
        product->setSellingPrice(sellingPriceSpinBox->value());
        product->setQuantity(quantitySpinBox->value());
        product->setMinQuantity(minQuantitySpinBox->value());

        // Save the product
        bool success;
        if (isNewProduct) {
            success = m_inventoryService.addProduct(product);
        } else {
            success = m_inventoryService.updateProduct(product);
        }

        if (!success) {
            QMessageBox::critical(this, tr("Error"),
                                 tr("Failed to save the product."));
            return false;
        }

        return true;
    }

    return false;
}

void InventoryWidget::updateProductModel()
{
    // Clear the model
    m_productsModel->removeRows(0, m_productsModel->rowCount());

    // Add products to the model
    for (const QSharedPointer<Product> &product : m_products) {
        QList<QStandardItem*> row;

        // ID (hidden, used for reference)
        QStandardItem *idItem = new QStandardItem(QString::number(product->id()));
        idItem->setData(product->id(), Qt::UserRole);
        row.append(idItem);

        // Code
        row.append(new QStandardItem(product->code()));

        // Name
        row.append(new QStandardItem(product->name()));

        // Category
        row.append(new QStandardItem(product->category()));

        // Purchase Price
        row.append(new QStandardItem(QString::number(product->purchasePrice(), 'f', 2)));

        // Selling Price
        row.append(new QStandardItem(QString::number(product->sellingPrice(), 'f', 2)));

        // Quantity
        QStandardItem *quantityItem = new QStandardItem(QString::number(product->quantity()));
        if (product->isLowStock()) {
            quantityItem->setForeground(Qt::red);
        }
        row.append(quantityItem);

        m_productsModel->appendRow(row);
    }

    // Resize columns to contents
    m_productsTable->resizeColumnsToContents();
}
