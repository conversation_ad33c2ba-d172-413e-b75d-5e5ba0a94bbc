#ifndef REPORTSWIDGET_H
#define REPORTSWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QDateEdit>
#include <QTabWidget>
#include <QTableView>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QtCharts/QChart>
#include <QtCharts/QChartView>
#include <QtCharts/QBarSeries>
#include <QtCharts/QBarSet>
#include <QtCharts/QBarCategoryAxis>
#include <QtCharts/QValueAxis>
#include <QtCharts/QPieSeries>
#include <QtCharts/QLineSeries>
#include <QtCharts/QDateTimeAxis>
#include <QtCharts/QSplineSeries>
#include <QMessageBox>
#include <QDebug>

#include "../../core/services/inventoryservice.h"
#include "../../core/services/salesservice.h"
#include "../../core/services/purchaseservice.h"

QT_CHARTS_USE_NAMESPACE

/**
 * @brief The ReportsWidget class displays various reports
 */
class ReportsWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ReportsWidget(QWidget *parent = nullptr);

private slots:
    /**
     * @brief Generate the selected report
     */
    void generateReport();

    /**
     * @brief Export the current report to a file
     */
    void exportReport();

    /**
     * @brief Print the current report
     */
    void printReport();

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create the sales report tab
     * @return Widget containing the sales report
     */
    QWidget* createSalesReportTab();

    /**
     * @brief Create the inventory report tab
     * @return Widget containing the inventory report
     */
    QWidget* createInventoryReportTab();

    /**
     * @brief Create the customer report tab
     * @return Widget containing the customer report
     */
    QWidget* createCustomerReportTab();

    /**
     * @brief Create the supplier report tab
     * @return Widget containing the supplier report
     */
    QWidget* createSupplierReportTab();

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_toolbarLayout;

    // Toolbar widgets
    QLabel *m_titleLabel;
    QComboBox *m_reportTypeCombo;
    QDateEdit *m_fromDateEdit;
    QDateEdit *m_toDateEdit;
    QPushButton *m_generateButton;
    QPushButton *m_exportButton;
    QPushButton *m_printButton;

    // Tab widget
    QTabWidget *m_tabWidget;

    // Report tabs
    QWidget *m_salesReportTab;
    QWidget *m_inventoryReportTab;
    QWidget *m_customerReportTab;
    QWidget *m_supplierReportTab;

    // Services
    InventoryService m_inventoryService;
    SalesService m_salesService;
    PurchaseService m_purchaseService;
};

#endif // REPORTSWIDGET_H
