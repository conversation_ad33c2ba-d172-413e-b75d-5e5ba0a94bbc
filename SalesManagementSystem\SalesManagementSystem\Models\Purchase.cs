using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    public class Purchase : INotifyPropertyChanged
    {
        private int _id;
        private string _invoiceNumber;
        private int? _supplierId;
        private string _supplierName;
        private DateTime _date;
        private decimal _subtotal;
        private decimal _discount;
        private decimal _tax;
        private decimal _total;
        private string _paymentMethod;
        private string _paymentStatus;
        private string _notes;
        private ObservableCollection<PurchaseItem> _items;
        private DateTime _createdAt;
        private DateTime? _updatedAt;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                if (_invoiceNumber != value)
                {
                    _invoiceNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? SupplierId
        {
            get => _supplierId;
            set
            {
                if (_supplierId != value)
                {
                    _supplierId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SupplierName
        {
            get => _supplierName;
            set
            {
                if (_supplierName != value)
                {
                    _supplierName = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime Date
        {
            get => _date;
            set
            {
                if (_date != value)
                {
                    _date = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    // Recalculate total when subtotal changes
                    CalculateTotal();
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    // Recalculate total when discount changes
                    CalculateTotal();
                }
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                if (_tax != value)
                {
                    _tax = value;
                    OnPropertyChanged();
                    // Recalculate total when tax changes
                    CalculateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set
            {
                if (_paymentMethod != value)
                {
                    _paymentMethod = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                if (_paymentStatus != value)
                {
                    _paymentStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<PurchaseItem> Items
        {
            get => _items ?? (_items = new ObservableCollection<PurchaseItem>());
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                    CalculateSubtotal();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public bool IsCurrentMonth => Date.Month == DateTime.Now.Month && Date.Year == DateTime.Now.Year;

        // Methods
        public void CalculateSubtotal()
        {
            decimal subtotal = 0;
            foreach (var item in Items)
            {
                subtotal += item.Total;
            }
            Subtotal = subtotal;
        }

        public void CalculateTotal()
        {
            Total = Subtotal - Discount + Tax;
        }

        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class PurchaseItem : INotifyPropertyChanged
    {
        private int _id;
        private int _purchaseId;
        private int _productId;
        private string _productName;
        private string _productCode;
        private int _quantity;
        private decimal _unitPrice;
        private decimal _discount;
        private decimal _total;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int PurchaseId
        {
            get => _purchaseId;
            set
            {
                if (_purchaseId != value)
                {
                    _purchaseId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ProductId
        {
            get => _productId;
            set
            {
                if (_productId != value)
                {
                    _productId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ProductCode
        {
            get => _productCode;
            set
            {
                if (_productCode != value)
                {
                    _productCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                if (_unitPrice != value)
                {
                    _unitPrice = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged();
                    CalculateTotal();
                }
            }
        }

        public decimal Total
        {
            get => _total;
            set
            {
                if (_total != value)
                {
                    _total = value;
                    OnPropertyChanged();
                }
            }
        }

        // Methods
        public void CalculateTotal()
        {
            Total = (Quantity * UnitPrice) - Discount;
        }

        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class PurchaseValidator : AbstractValidator<Purchase>
    {
        public PurchaseValidator()
        {
            RuleFor(p => p.InvoiceNumber).NotEmpty().WithMessage("Invoice number is required");
            RuleFor(p => p.Date).NotEmpty().WithMessage("Date is required");
            RuleFor(p => p.PaymentMethod).NotEmpty().WithMessage("Payment method is required")
                .Must(pm => new[] { "Cash", "Credit Card", "Bank Transfer", "Check", "Other" }.Contains(pm))
                .WithMessage("Invalid payment method");
            RuleFor(p => p.PaymentStatus).NotEmpty().WithMessage("Payment status is required")
                .Must(ps => new[] { "Paid", "Unpaid", "Partial", "Cancelled" }.Contains(ps))
                .WithMessage("Invalid payment status");
            RuleFor(p => p.Items).NotEmpty().WithMessage("At least one item is required");
        }
    }

    public class PurchaseItemValidator : AbstractValidator<PurchaseItem>
    {
        public PurchaseItemValidator()
        {
            RuleFor(pi => pi.ProductId).NotEmpty().WithMessage("Product is required");
            RuleFor(pi => pi.Quantity).GreaterThan(0).WithMessage("Quantity must be greater than 0");
            RuleFor(pi => pi.UnitPrice).GreaterThan(0).WithMessage("Unit price must be greater than 0");
            RuleFor(pi => pi.Discount).GreaterThanOrEqualTo(0).WithMessage("Discount cannot be negative");
        }
    }
}