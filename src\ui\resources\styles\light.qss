/* Modern Light Mode Stylesheet */

/* Global */
QWidget {
    background-color: #ffffff;
    color: #24292f;
    font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    font-size: 9pt;
}

/* Main Window */
QMainWindow {
    background-color: #f6f8fa;
    border: none;
}

/* Menu Bar */
QMenuBar {
    background-color: #F0F0F0;
    color: #000000;
}

QMenuBar::item {
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #E0E0E0;
}

QMenu {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
}

QMenu::item:selected {
    background-color: #E0E0E0;
}

/* Tool Bar */
QToolBar {
    background-color: #F0F0F0;
    border: none;
}

/* Status Bar */
QStatusBar {
    background-color: #F0F0F0;
    color: #000000;
}

/* Side Bar */
QFrame#sideBar {
    background-color: #ffffff;
    border-right: 1px solid #d0d7de;
    min-width: 220px;
    max-width: 220px;
}

QLabel#logoLabel {
    color: #0969da;
    font-size: 18pt;
    font-weight: bold;
    padding: 20px 0px;
}

QToolButton {
    background-color: transparent;
    color: #24292f;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    text-align: left;
    font-size: 10pt;
    font-weight: 500;
    margin: 2px 8px;
    min-height: 20px;
}

QToolButton:hover {
    background-color: #f3f4f6;
    color: #0969da;
}

QToolButton:checked {
    background-color: #0969da;
    color: #ffffff;
    font-weight: 600;
}

/* Buttons */
QPushButton {
    background-color: #1f883d;
    color: #ffffff;
    border: 1px solid #1a7f37;
    border-radius: 6px;
    padding: 8px 16px;
    min-width: 80px;
    font-weight: 500;
    font-size: 9pt;
}

QPushButton:hover {
    background-color: #1a7f37;
    border-color: #166e2c;
    transform: translateY(-1px);
}

QPushButton:pressed {
    background-color: #166e2c;
    border-color: #145c26;
    transform: translateY(0px);
}

QPushButton:disabled {
    background-color: #f3f4f6;
    color: #8c959f;
    border-color: #d0d7de;
}

/* Primary Button */
QPushButton[class="primary"] {
    background-color: #0969da;
    border-color: #0969da;
}

QPushButton[class="primary"]:hover {
    background-color: #0860ca;
    border-color: #0860ca;
}

/* Danger Button */
QPushButton[class="danger"] {
    background-color: #cf222e;
    border-color: #cf222e;
}

QPushButton[class="danger"]:hover {
    background-color: #a40e26;
    border-color: #a40e26;
}

/* Line Edit */
QLineEdit {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus {
    border: 1px solid #0078D7;
}

/* Combo Box */
QComboBox {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
    border-radius: 4px;
    padding: 4px;
}

QComboBox:focus {
    border: 1px solid #0078D7;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #C0C0C0;
}

QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
    selection-background-color: #0078D7;
    selection-color: #FFFFFF;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox, QDateEdit {
    background-color: #FFFFFF;
    color: #000000;
    border: 1px solid #C0C0C0;
    border-radius: 4px;
    padding: 4px;
}

QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border: 1px solid #0078D7;
}

/* Table View */
QTableView {
    background-color: #FFFFFF;
    color: #000000;
    gridline-color: #E0E0E0;
    border: 1px solid #C0C0C0;
}

QTableView::item {
    padding: 4px;
}

QTableView::item:selected {
    background-color: #0078D7;
    color: #FFFFFF;
}

QHeaderView::section {
    background-color: #F0F0F0;
    color: #000000;
    padding: 4px;
    border: 1px solid #C0C0C0;
}

QTableView::item:alternate {
    background-color: #F8F8F8;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #C0C0C0;
}

QTabBar::tab {
    background-color: #F0F0F0;
    color: #000000;
    border: 1px solid #C0C0C0;
    border-bottom: none;
    padding: 6px 12px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

/* Scroll Bar */
QScrollBar:vertical {
    background-color: #F0F0F0;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #C0C0C0;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #0078D7;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #F0F0F0;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #C0C0C0;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #0078D7;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Dialog */
QDialog {
    background-color: #F0F0F0;
    color: #000000;
}

/* Group Box */
QGroupBox {
    border: 1px solid #C0C0C0;
    border-radius: 4px;
    margin-top: 8px;
    padding-top: 8px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 3px;
}

/* Charts */
QChartView {
    background-color: #FFFFFF;
}

/* Summary Cards */
QFrame#summaryCard {
    border-radius: 10px;
}
