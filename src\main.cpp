#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QMessageBox>
#include <QDebug>
#include <QTextCodec>
#include <QDir>
#include <QStandardPaths>
#include "ui/mainwindow.h"
#include "core/database/databasemanager.h"
#include "core/utils/translationmanager.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    // Set application information
    QApplication::setApplicationName("Accountant");
    QApplication::setApplicationDisplayName("المحاسب - Accountant");
    QApplication::setApplicationVersion("1.0");
    QApplication::setOrganizationName("YourCompany");

    // Set UTF-8 encoding for proper Arabic support (Qt5)
    #if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    #endif

    // Initialize translation manager
    TranslationManager translationManager;
    translationManager.initialize(&app);

    // Initialize database
    DatabaseManager dbManager;
    if (!dbManager.initialize()) {
        QMessageBox::critical(nullptr, "Database Error",
                             "Failed to initialize database. The application will now exit.");
        return -1;
    }

    // Create and show main window
    MainWindow mainWindow;

    // Set translation manager as a child of the application for global access
    translationManager.setParent(&app);

    // Connect translation manager to main window for language updates
    QObject::connect(&translationManager, &TranslationManager::languageChanged,
                     &mainWindow, &MainWindow::retranslateUI);

    mainWindow.show();

    return app.exec();
}
