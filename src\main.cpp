#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QMessageBox>
#include <QDebug>
#include "ui/mainwindow.h"
#include "core/database/databasemanager.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application information
    QApplication::setApplicationName("Accountant");
    QApplication::setApplicationVersion("0.1");
    QApplication::setOrganizationName("YourCompany");
    
    // Load translations
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "Accountant_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }
    
    // Initialize database
    DatabaseManager dbManager;
    if (!dbManager.initialize()) {
        QMessageBox::critical(nullptr, "Database Error", 
                             "Failed to initialize database. The application will now exit.");
        return -1;
    }
    
    // Create and show main window
    MainWindow mainWindow;
    mainWindow.show();
    
    return app.exec();
}
