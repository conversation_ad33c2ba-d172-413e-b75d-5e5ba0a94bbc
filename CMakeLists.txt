cmake_minimum_required(VERSION 3.14)

project(AccountantApp VERSION 0.1 LANGUAGES CXX)

set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt packages
find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets Sql Charts)
set(QT_VERSION_MAJOR 5)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui
)

# Source files
set(SOURCES
    src/main.cpp
    src/core/database/databasemanager.cpp
    src/core/models/product.cpp
    src/core/models/customer.cpp
    src/core/models/supplier.cpp
    src/core/models/invoice.cpp
    src/core/services/inventoryservice.cpp
    src/core/services/salesservice.cpp
    src/core/services/purchaseservice.cpp
    src/ui/mainwindow.cpp
    src/ui/widgets/dashboardwidget.cpp
    src/ui/widgets/inventorywidget.cpp
    src/ui/widgets/saleswidget.cpp
    src/ui/widgets/customerswidget.cpp
    src/ui/widgets/supplierswidget.cpp
    src/ui/widgets/reportswidget.cpp
    src/ui/widgets/invoicedialog.cpp
    src/ui/widgets/iconhelper.cpp
)

# Header files
set(HEADERS
    src/core/database/databasemanager.h
    src/core/models/product.h
    src/core/models/customer.h
    src/core/models/supplier.h
    src/core/models/invoice.h
    src/core/services/inventoryservice.h
    src/core/services/salesservice.h
    src/core/services/purchaseservice.h
    src/ui/mainwindow.h
    src/ui/widgets/dashboardwidget.h
    src/ui/widgets/inventorywidget.h
    src/ui/widgets/saleswidget.h
    src/ui/widgets/customerswidget.h
    src/ui/widgets/supplierswidget.h
    src/ui/widgets/reportswidget.h
    src/ui/widgets/invoicedialog.h
    src/ui/widgets/iconhelper.h
)

# UI files (none for now, using code-based UI)
set(UI_FILES
)

# Resources
set(RESOURCES
    src/ui/resources/resources.qrc
)

# Create executable
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Gui
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Sql
    Qt${QT_VERSION_MAJOR}::Charts
)

# Install
install(TARGETS ${PROJECT_NAME}
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
