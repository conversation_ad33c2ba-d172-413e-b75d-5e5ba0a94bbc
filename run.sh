#!/bin/bash

echo "Starting Accountant App using Docker..."
echo "Make sure you have an X server running (VcXsrv on Windows, XQuartz on Mac)"

# Detect OS
OS="$(uname -s)"
case "${OS}" in
    Linux*)
        echo "Linux detected"
        # Allow X server connections from Docker
        xhost +local:docker || echo "Warning: Could not set xhost permissions. Make sure X server is running."
        ;;
    Darwin*)
        echo "macOS detected"
        # For macOS with XQuartz
        xhost +localhost || echo "Warning: Could not set xhost permissions. Make sure XQuartz is running."
        # Update docker-compose.yml for Mac
        sed -i '' 's/network_mode: "host"/#network_mode: "host"/g' docker-compose.yml
        sed -i '' 's/# extra_hosts:/extra_hosts:/g' docker-compose.yml
        sed -i '' 's/#  - "host.docker.internal:host-gateway"/  - "host.docker.internal:host-gateway"/g' docker-compose.yml
        ;;
    MINGW*|MSYS*|CYGWIN*)
        echo "Windows detected"
        # For Windows, no xhost command typically available
        # Update docker-compose.yml for Windows
        sed -i 's/network_mode: "host"/#network_mode: "host"/g' docker-compose.yml
        sed -i 's/# extra_hosts:/extra_hosts:/g' docker-compose.yml
        sed -i 's/#  - "host.docker.internal:host-gateway"/  - "host.docker.internal:host-gateway"/g' docker-compose.yml
        ;;
    *)
        echo "Unknown OS: ${OS}"
        ;;
esac

# Create database directory if it doesn't exist
mkdir -p database

# Build and run the Docker container
echo "Building and starting Docker container..."
docker-compose up --build
