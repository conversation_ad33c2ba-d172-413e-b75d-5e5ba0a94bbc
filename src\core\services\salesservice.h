#ifndef SALESSERVICE_H
#define SALESSERVICE_H

#include <QObject>
#include <QList>
#include <QSharedPointer>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include "../models/invoice.h"
#include "../models/customer.h"
#include "../database/databasemanager.h"
#include "inventoryservice.h"

/**
 * @brief The SalesService class handles sales operations
 */
class SalesService : public QObject
{
    Q_OBJECT
    
public:
    explicit SalesService(QObject *parent = nullptr);
    
    /**
     * @brief Get all sales invoices
     * @return List of sales invoices
     */
    QList<QSharedPointer<Invoice>> getAllSalesInvoices();
    
    /**
     * @brief Get sales invoice by ID
     * @param id Invoice ID
     * @return Invoice pointer or null if not found
     */
    QSharedPointer<Invoice> getSalesInvoiceById(int id);
    
    /**
     * @brief Get sales invoice by invoice number
     * @param invoiceNumber Invoice number
     * @return Invoice pointer or null if not found
     */
    QSharedPointer<Invoice> getSalesInvoiceByNumber(const QString &invoiceNumber);
    
    /**
     * @brief Create a new sales invoice
     * @param invoice Invoice to create
     * @return true if successful, false otherwise
     */
    bool createSalesInvoice(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Update an existing sales invoice
     * @param invoice Invoice to update
     * @return true if successful, false otherwise
     */
    bool updateSalesInvoice(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Delete a sales invoice
     * @param id Invoice ID
     * @return true if successful, false otherwise
     */
    bool deleteSalesInvoice(int id);
    
    /**
     * @brief Add payment to a sales invoice
     * @param invoiceId Invoice ID
     * @param amount Payment amount
     * @param paymentMethod Payment method
     * @param reference Payment reference
     * @param notes Payment notes
     * @return true if successful, false otherwise
     */
    bool addPayment(int invoiceId, double amount, const QString &paymentMethod,
                    const QString &reference, const QString &notes);
    
    /**
     * @brief Get all customers
     * @return List of customers
     */
    QList<QSharedPointer<Customer>> getAllCustomers();
    
    /**
     * @brief Get customer by ID
     * @param id Customer ID
     * @return Customer pointer or null if not found
     */
    QSharedPointer<Customer> getCustomerById(int id);
    
    /**
     * @brief Add a new customer
     * @param customer Customer to add
     * @return true if successful, false otherwise
     */
    bool addCustomer(const QSharedPointer<Customer> &customer);
    
    /**
     * @brief Update an existing customer
     * @param customer Customer to update
     * @return true if successful, false otherwise
     */
    bool updateCustomer(const QSharedPointer<Customer> &customer);
    
    /**
     * @brief Delete a customer
     * @param id Customer ID
     * @return true if successful, false otherwise
     */
    bool deleteCustomer(int id);
    
    /**
     * @brief Search customers by name or phone
     * @param searchTerm Search term
     * @return List of matching customers
     */
    QList<QSharedPointer<Customer>> searchCustomers(const QString &searchTerm);
    
    /**
     * @brief Generate a new invoice number
     * @return New invoice number
     */
    QString generateInvoiceNumber();
    
private:
    /**
     * @brief Create an Invoice object from a SQL query
     * @param query SQL query with invoice data
     * @return Invoice pointer
     */
    QSharedPointer<Invoice> createInvoiceFromQuery(QSqlQuery &query);
    
    /**
     * @brief Create a Customer object from a SQL query
     * @param query SQL query with customer data
     * @return Customer pointer
     */
    QSharedPointer<Customer> createCustomerFromQuery(QSqlQuery &query);
    
    /**
     * @brief Load invoice items for an invoice
     * @param invoice Invoice to load items for
     * @return true if successful, false otherwise
     */
    bool loadInvoiceItems(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Save invoice items for an invoice
     * @param invoice Invoice to save items for
     * @return true if successful, false otherwise
     */
    bool saveInvoiceItems(const QSharedPointer<Invoice> &invoice);
    
    /**
     * @brief Update customer balance
     * @param customerId Customer ID
     * @param amount Amount to add to balance (negative to subtract)
     * @return true if successful, false otherwise
     */
    bool updateCustomerBalance(int customerId, double amount);
    
    /**
     * @brief Update inventory quantities based on invoice items
     * @param invoice Invoice with items
     * @param isCreating Whether the invoice is being created (true) or deleted (false)
     * @return true if successful, false otherwise
     */
    bool updateInventory(const QSharedPointer<Invoice> &invoice, bool isCreating);
    
    DatabaseManager m_dbManager;
    InventoryService m_inventoryService;
};

#endif // SALESSERVICE_H
