#include "supplierswidget.h"

SuppliersWidget::SuppliersWidget(QWidget *parent)
    : QWidget(parent),
      m_purchaseService(this)
{
    createUI();
    loadSuppliers();
}

void SuppliersWidget::loadSuppliers()
{
    // Get suppliers from service
    m_suppliers = m_purchaseService.getAllSuppliers();

    // Update the model
    updateSupplierModel();
}

void SuppliersWidget::addSupplier()
{
    if (showSupplierDialog()) {
        loadSuppliers();
    }
}

void SuppliersWidget::editSupplier()
{
    // Get the selected supplier
    QModelIndex currentIndex = m_suppliersTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a supplier to edit."));
        return;
    }

    // Get the supplier ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int supplierId = m_suppliersModel->data(m_suppliersModel->index(row, 0), Qt::UserRole).toInt();

    // Find the supplier in the list
    QSharedPointer<Supplier> supplier;
    for (const QSharedPointer<Supplier> &s : m_suppliers) {
        if (s->id() == supplierId) {
            supplier = s;
            break;
        }
    }

    if (!supplier) {
        QMessageBox::warning(this, tr("Error"), tr("Supplier not found."));
        return;
    }

    // Show the edit dialog
    if (showSupplierDialog(supplier)) {
        loadSuppliers();
    }
}

void SuppliersWidget::deleteSupplier()
{
    // Get the selected supplier
    QModelIndex currentIndex = m_suppliersTable->selectionModel()->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, tr("No Selection"), tr("Please select a supplier to delete."));
        return;
    }

    // Get the supplier ID from the model
    QModelIndex sourceIndex = m_proxyModel->mapToSource(currentIndex);
    int row = sourceIndex.row();
    int supplierId = m_suppliersModel->data(m_suppliersModel->index(row, 0), Qt::UserRole).toInt();
    QString supplierName = m_suppliersModel->data(m_suppliersModel->index(row, 1)).toString();

    // Confirm deletion
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, tr("Confirm Deletion"),
        tr("Are you sure you want to delete the supplier '%1'?").arg(supplierName),
        QMessageBox::Yes | QMessageBox::No
    );

    if (reply == QMessageBox::Yes) {
        // Delete the supplier
        if (m_purchaseService.deleteSupplier(supplierId)) {
            loadSuppliers();
        } else {
            QMessageBox::critical(this, tr("Error"), tr("Failed to delete the supplier. The supplier may have associated invoices."));
        }
    }
}

void SuppliersWidget::filterSuppliers()
{
    QString searchText = m_searchEdit->text();

    // Set the filter for the proxy model
    m_proxyModel->setFilterFixedString(searchText);
}

void SuppliersWidget::onSupplierDoubleClicked(const QModelIndex &index)
{
    if (index.isValid()) {
        editSupplier();
    }
}

void SuppliersWidget::createUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    // Title
    m_titleLabel = new QLabel(tr("Supplier Management"), this);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(18);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_mainLayout->addWidget(m_titleLabel);

    // Toolbar
    m_toolbarLayout = new QHBoxLayout();
    m_toolbarLayout->setSpacing(10);

    m_addButton = new QPushButton("🏭 Add Supplier", this);
    m_addButton->setProperty("class", "primary");
    connect(m_addButton, &QPushButton::clicked, this, &SuppliersWidget::addSupplier);

    m_editButton = new QPushButton("✏️ Edit", this);
    m_editButton->setEnabled(false);
    connect(m_editButton, &QPushButton::clicked, this, &SuppliersWidget::editSupplier);

    m_deleteButton = new QPushButton("🗑️ Delete", this);
    m_deleteButton->setProperty("class", "danger");
    m_deleteButton->setEnabled(false);
    connect(m_deleteButton, &QPushButton::clicked, this, &SuppliersWidget::deleteSupplier);

    m_refreshButton = new QPushButton("🔄 Refresh", this);
    connect(m_refreshButton, &QPushButton::clicked, this, &SuppliersWidget::loadSuppliers);

    m_toolbarLayout->addWidget(m_addButton);
    m_toolbarLayout->addWidget(m_editButton);
    m_toolbarLayout->addWidget(m_deleteButton);
    m_toolbarLayout->addStretch();
    m_toolbarLayout->addWidget(m_refreshButton);

    m_mainLayout->addLayout(m_toolbarLayout);

    // Search bar
    m_searchLayout = new QHBoxLayout();
    m_searchLayout->setSpacing(10);

    QLabel *searchLabel = new QLabel(tr("Search:"), this);
    m_searchEdit = new QLineEdit(this);
    m_searchEdit->setPlaceholderText(tr("Enter supplier name or phone"));
    connect(m_searchEdit, &QLineEdit::textChanged, this, &SuppliersWidget::filterSuppliers);

    m_searchButton = new QPushButton(tr("Search"), this);
    m_searchButton->setIcon(QIcon::fromTheme("system-search", QIcon(":/icons/search.png")));
    connect(m_searchButton, &QPushButton::clicked, this, &SuppliersWidget::filterSuppliers);

    m_searchLayout->addWidget(searchLabel);
    m_searchLayout->addWidget(m_searchEdit, 1);
    m_searchLayout->addWidget(m_searchButton);

    m_mainLayout->addLayout(m_searchLayout);

    // Suppliers table
    m_suppliersTable = new QTableView(this);
    m_suppliersTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_suppliersTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_suppliersTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_suppliersTable->setAlternatingRowColors(true);
    m_suppliersTable->setSortingEnabled(true);
    m_suppliersTable->horizontalHeader()->setStretchLastSection(true);
    m_suppliersTable->verticalHeader()->setVisible(false);

    // Create the model
    m_suppliersModel = new QStandardItemModel(0, 5, this);
    m_suppliersModel->setHorizontalHeaderLabels({
        tr("ID"), tr("Name"), tr("Phone"), tr("Email"), tr("Balance")
    });

    // Create the proxy model for filtering and sorting
    m_proxyModel = new QSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_suppliersModel);
    m_proxyModel->setFilterKeyColumn(1); // Filter by name
    m_proxyModel->setFilterCaseSensitivity(Qt::CaseInsensitive);

    m_suppliersTable->setModel(m_proxyModel);

    // Hide the ID column
    m_suppliersTable->hideColumn(0);

    // Connect signals
    connect(m_suppliersTable, &QTableView::doubleClicked,
            this, &SuppliersWidget::onSupplierDoubleClicked);
    connect(m_suppliersTable->selectionModel(), &QItemSelectionModel::selectionChanged,
            [this]() {
                bool hasSelection = m_suppliersTable->selectionModel()->hasSelection();
                m_editButton->setEnabled(hasSelection);
                m_deleteButton->setEnabled(hasSelection);
            });

    m_mainLayout->addWidget(m_suppliersTable);
}

bool SuppliersWidget::showSupplierDialog(QSharedPointer<Supplier> supplier)
{
    // Create a new supplier if none was provided
    bool isNewSupplier = !supplier;
    if (isNewSupplier) {
        supplier = QSharedPointer<Supplier>(new Supplier());
    }

    // Create the dialog
    QDialog dialog(this);
    dialog.setWindowTitle(isNewSupplier ? tr("Add Supplier") : tr("Edit Supplier"));
    dialog.setMinimumWidth(400);

    QVBoxLayout *dialogLayout = new QVBoxLayout(&dialog);

    // Form layout
    QFormLayout *formLayout = new QFormLayout();

    // Name
    QLineEdit *nameEdit = new QLineEdit(&dialog);
    nameEdit->setText(supplier->name());
    formLayout->addRow(tr("Name:"), nameEdit);

    // Phone
    QLineEdit *phoneEdit = new QLineEdit(&dialog);
    phoneEdit->setText(supplier->phone());
    formLayout->addRow(tr("Phone:"), phoneEdit);

    // Email
    QLineEdit *emailEdit = new QLineEdit(&dialog);
    emailEdit->setText(supplier->email());
    formLayout->addRow(tr("Email:"), emailEdit);

    // Address
    QLineEdit *addressEdit = new QLineEdit(&dialog);
    addressEdit->setText(supplier->address());
    formLayout->addRow(tr("Address:"), addressEdit);

    // Balance
    QDoubleSpinBox *balanceSpinBox = new QDoubleSpinBox(&dialog);
    balanceSpinBox->setRange(-1000000, 1000000);
    balanceSpinBox->setDecimals(2);
    balanceSpinBox->setValue(supplier->balance());
    balanceSpinBox->setEnabled(!isNewSupplier); // Only allow editing balance for existing suppliers
    formLayout->addRow(tr("Balance:"), balanceSpinBox);

    dialogLayout->addLayout(formLayout);

    // Buttons
    QDialogButtonBox *buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel,
        Qt::Horizontal, &dialog);
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &dialog, &QDialog::reject);

    dialogLayout->addWidget(buttonBox);

    // Show the dialog
    if (dialog.exec() == QDialog::Accepted) {
        // Update the supplier with the new values
        supplier->setName(nameEdit->text());
        supplier->setPhone(phoneEdit->text());
        supplier->setEmail(emailEdit->text());
        supplier->setAddress(addressEdit->text());
        supplier->setBalance(balanceSpinBox->value());

        // Save the supplier
        bool success;
        if (isNewSupplier) {
            success = m_purchaseService.addSupplier(supplier);
        } else {
            success = m_purchaseService.updateSupplier(supplier);
        }

        if (!success) {
            QMessageBox::critical(this, tr("Error"),
                                 tr("Failed to save the supplier."));
            return false;
        }

        return true;
    }

    return false;
}

void SuppliersWidget::updateSupplierModel()
{
    // Clear the model
    m_suppliersModel->removeRows(0, m_suppliersModel->rowCount());

    // Add suppliers to the model
    for (const QSharedPointer<Supplier> &supplier : m_suppliers) {
        QList<QStandardItem*> row;

        // ID (hidden, used for reference)
        QStandardItem *idItem = new QStandardItem(QString::number(supplier->id()));
        idItem->setData(supplier->id(), Qt::UserRole);
        row.append(idItem);

        // Name
        row.append(new QStandardItem(supplier->name()));

        // Phone
        row.append(new QStandardItem(supplier->phone()));

        // Email
        row.append(new QStandardItem(supplier->email()));

        // Balance
        QStandardItem *balanceItem = new QStandardItem(QString::number(supplier->balance(), 'f', 2));
        if (supplier->hasCredit()) {
            balanceItem->setForeground(Qt::red);
        }
        row.append(balanceItem);

        m_suppliersModel->appendRow(row);
    }

    // Resize columns to contents
    m_suppliersTable->resizeColumnsToContents();
}
