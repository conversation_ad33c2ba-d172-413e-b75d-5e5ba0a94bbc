<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Assets\Images\app_icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Assets\Images\app_icon.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MahApps.Metro" Version="2.4.9" />
    <PackageReference Include="MaterialDesignThemes" Version="4.6.1" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc2" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.116" />
    <PackageReference Include="Prism.Core" Version="8.1.97" />
    <PackageReference Include="FluentValidation" Version="11.2.2" />
    <PackageReference Include="NLog" Version="5.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\Images\app_icon.ico" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\Fonts\" />
    <Folder Include="Assets\Styles\" />
    <Folder Include="Models\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Resources\Localization\" />
    <Folder Include="Resources\Themes\" />
  </ItemGroup>

</Project>