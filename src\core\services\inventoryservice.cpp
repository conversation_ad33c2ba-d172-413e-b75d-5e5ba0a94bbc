#include "inventoryservice.h"

InventoryService::InventoryService(QObject *parent)
    : QObject(parent)
{
    // Initialize database
    m_dbManager.initialize();
}

QList<QSharedPointer<Product>> InventoryService::getAllProducts()
{
    QList<QSharedPointer<Product>> products;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM products ORDER BY name");
    
    if (query.exec()) {
        while (query.next()) {
            products.append(createProductFromQuery(query));
        }
    } else {
        qDebug() << "Failed to get products:" << query.lastError().text();
    }
    
    return products;
}

QSharedPointer<Product> InventoryService::getProductById(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM products WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        return createProductFromQuery(query);
    } else {
        qDebug() << "Failed to get product by ID:" << query.lastError().text();
        return nullptr;
    }
}

QSharedPointer<Product> InventoryService::getProductByCode(const QString &code)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM products WHERE code = ?");
    query.addBindValue(code);
    
    if (query.exec() && query.next()) {
        return createProductFromQuery(query);
    } else {
        qDebug() << "Failed to get product by code:" << query.lastError().text();
        return nullptr;
    }
}

bool InventoryService::addProduct(const QSharedPointer<Product> &product)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("INSERT INTO products (code, name, description, category, purchase_price, "
                  "selling_price, quantity, min_quantity) "
                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    
    query.addBindValue(product->code());
    query.addBindValue(product->name());
    query.addBindValue(product->description());
    query.addBindValue(product->category());
    query.addBindValue(product->purchasePrice());
    query.addBindValue(product->sellingPrice());
    query.addBindValue(product->quantity());
    query.addBindValue(product->minQuantity());
    
    if (query.exec()) {
        // Set the ID of the newly added product
        product->setId(query.lastInsertId().toInt());
        return true;
    } else {
        qDebug() << "Failed to add product:" << query.lastError().text();
        return false;
    }
}

bool InventoryService::updateProduct(const QSharedPointer<Product> &product)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE products SET code = ?, name = ?, description = ?, category = ?, "
                  "purchase_price = ?, selling_price = ?, quantity = ?, min_quantity = ?, "
                  "updated_at = CURRENT_TIMESTAMP "
                  "WHERE id = ?");
    
    query.addBindValue(product->code());
    query.addBindValue(product->name());
    query.addBindValue(product->description());
    query.addBindValue(product->category());
    query.addBindValue(product->purchasePrice());
    query.addBindValue(product->sellingPrice());
    query.addBindValue(product->quantity());
    query.addBindValue(product->minQuantity());
    query.addBindValue(product->id());
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update product:" << query.lastError().text();
        return false;
    }
}

bool InventoryService::deleteProduct(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("DELETE FROM products WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to delete product:" << query.lastError().text();
        return false;
    }
}

bool InventoryService::updateProductQuantity(int id, int quantity)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE products SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    query.addBindValue(quantity);
    query.addBindValue(id);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update product quantity:" << query.lastError().text();
        return false;
    }
}

QList<QSharedPointer<Product>> InventoryService::getLowStockProducts()
{
    QList<QSharedPointer<Product>> products;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM products WHERE quantity <= min_quantity ORDER BY name");
    
    if (query.exec()) {
        while (query.next()) {
            products.append(createProductFromQuery(query));
        }
    } else {
        qDebug() << "Failed to get low stock products:" << query.lastError().text();
    }
    
    return products;
}

QList<QSharedPointer<Product>> InventoryService::searchProducts(const QString &searchTerm)
{
    QList<QSharedPointer<Product>> products;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM products WHERE name LIKE ? OR code LIKE ? ORDER BY name");
    query.addBindValue("%" + searchTerm + "%");
    query.addBindValue("%" + searchTerm + "%");
    
    if (query.exec()) {
        while (query.next()) {
            products.append(createProductFromQuery(query));
        }
    } else {
        qDebug() << "Failed to search products:" << query.lastError().text();
    }
    
    return products;
}

QSharedPointer<Product> InventoryService::createProductFromQuery(QSqlQuery &query)
{
    QSharedPointer<Product> product = QSharedPointer<Product>(new Product());
    
    product->setId(query.value("id").toInt());
    product->setCode(query.value("code").toString());
    product->setName(query.value("name").toString());
    product->setDescription(query.value("description").toString());
    product->setCategory(query.value("category").toString());
    product->setPurchasePrice(query.value("purchase_price").toDouble());
    product->setSellingPrice(query.value("selling_price").toDouble());
    product->setQuantity(query.value("quantity").toInt());
    product->setMinQuantity(query.value("min_quantity").toInt());
    product->setCreatedAt(query.value("created_at").toDateTime());
    product->setUpdatedAt(query.value("updated_at").toDateTime());
    
    return product;
}
