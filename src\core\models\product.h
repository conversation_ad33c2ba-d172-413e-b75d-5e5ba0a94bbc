#ifndef PRODUCT_H
#define PRODUCT_H

#include <QString>
#include <QDateTime>

/**
 * @brief The Product class represents a product in the inventory
 */
class Product
{
public:
    Product();
    Product(int id, const QString &code, const QString &name, const QString &description,
            const QString &category, double purchasePrice, double sellingPrice,
            int quantity, int minQuantity);
    
    // Getters
    int id() const;
    QString code() const;
    QString name() const;
    QString description() const;
    QString category() const;
    double purchasePrice() const;
    double sellingPrice() const;
    int quantity() const;
    int minQuantity() const;
    QDateTime createdAt() const;
    QDateTime updatedAt() const;
    
    // Setters
    void setId(int id);
    void setCode(const QString &code);
    void setName(const QString &name);
    void setDescription(const QString &description);
    void setCategory(const QString &category);
    void setPurchasePrice(double price);
    void setSellingPrice(double price);
    void setQuantity(int quantity);
    void setMinQuantity(int quantity);
    void setCreatedAt(const QDateTime &dateTime);
    void setUpdatedAt(const QDateTime &dateTime);
    
    // Business logic
    double profit() const;
    bool isLowStock() const;
    
private:
    int m_id;
    QString m_code;
    QString m_name;
    QString m_description;
    QString m_category;
    double m_purchasePrice;
    double m_sellingPrice;
    int m_quantity;
    int m_minQuantity;
    QDateTime m_createdAt;
    QDateTime m_updatedAt;
};

#endif // PRODUCT_H
