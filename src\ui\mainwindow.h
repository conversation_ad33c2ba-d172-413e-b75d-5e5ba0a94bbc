#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QToolButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QAction>
#include <QMenu>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QMessageBox>
#include <QCloseEvent>
#include <QSettings>
#include <QDateTime>
#include <QTimer>
#include <QIcon>
#include <QFont>
#include <QFontDatabase>
#include <QApplication>
#include <QDesktopWidget>
#include <QScreen>
#include <QStyleFactory>
#include <QFile>
#include <QTextStream>
#include <QDebug>

#include "widgets/dashboardwidget.h"
#include "widgets/inventorywidget.h"
#include "widgets/saleswidget.h"
#include "widgets/customerswidget.h"
#include "widgets/supplierswidget.h"
#include "widgets/reportswidget.h"
#include "widgets/iconhelper.h"
#include "../core/utils/translationmanager.h"

/**
 * @brief The MainWindow class is the main application window
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    /**
     * @brief Switch to the dashboard page
     */
    void showDashboard();

    /**
     * @brief Switch to the inventory page
     */
    void showInventory();

    /**
     * @brief Switch to the sales page
     */
    void showSales();

    /**
     * @brief Switch to the customers page
     */
    void showCustomers();

    /**
     * @brief Switch to the suppliers page
     */
    void showSuppliers();

    /**
     * @brief Switch to the reports page
     */
    void showReports();

    /**
     * @brief Show the about dialog
     */
    void showAboutDialog();

    /**
     * @brief Show the settings dialog
     */
    void showSettingsDialog();

    /**
     * @brief Update the status bar with the current time
     */
    void updateStatusBar();

    /**
     * @brief Toggle the dark mode
     */
    void toggleDarkMode();

    /**
     * @brief Switch to English language
     */
    void switchToEnglish();

    /**
     * @brief Switch to Arabic language
     */
    void switchToArabic();

public slots:
    /**
     * @brief Retranslate UI when language changes
     */
    void retranslateUI();

private:
    /**
     * @brief Create the UI components
     */
    void createUI();

    /**
     * @brief Create the menu bar
     */
    void createMenuBar();

    /**
     * @brief Create the tool bar
     */
    void createToolBar();

    /**
     * @brief Create the status bar
     */
    void createStatusBar();

    /**
     * @brief Create the side bar
     */
    void createSideBar();

    /**
     * @brief Create the central widget
     */
    void createCentralWidget();

    /**
     * @brief Load the application settings
     */
    void loadSettings();

    /**
     * @brief Save the application settings
     */
    void saveSettings();

    /**
     * @brief Apply the dark mode style
     */
    void applyDarkMode();

    /**
     * @brief Apply the light mode style
     */
    void applyLightMode();

    // UI components
    QWidget *m_centralWidget;
    QHBoxLayout *m_mainLayout;
    QFrame *m_sideBar;
    QVBoxLayout *m_sideBarLayout;
    QStackedWidget *m_stackedWidget;
    QLabel *m_statusLabel;
    QLabel *m_timeLabel;
    QTimer *m_timer;

    // Sidebar buttons
    QToolButton *m_dashboardButton;
    QToolButton *m_inventoryButton;
    QToolButton *m_salesButton;
    QToolButton *m_customersButton;
    QToolButton *m_suppliersButton;
    QToolButton *m_reportsButton;

    // Page widgets
    DashboardWidget *m_dashboardWidget;
    InventoryWidget *m_inventoryWidget;
    SalesWidget *m_salesWidget;
    CustomersWidget *m_customersWidget;
    SuppliersWidget *m_suppliersWidget;
    ReportsWidget *m_reportsWidget;

    // Actions
    QAction *m_exitAction;
    QAction *m_aboutAction;
    QAction *m_settingsAction;
    QAction *m_darkModeAction;
    QAction *m_englishAction;
    QAction *m_arabicAction;

    // Settings
    bool m_darkMode;

    // Translation
    TranslationManager *m_translationManager;
};

#endif // MAINWINDOW_H
