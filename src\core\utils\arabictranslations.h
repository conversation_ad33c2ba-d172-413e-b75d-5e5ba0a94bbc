#ifndef ARABICTRANSLATIONS_H
#define ARABICTRANSLATIONS_H

#include <QHash>
#include <QString>

/**
 * @brief Simple Arabic translations for the application
 */
class ArabicTranslations
{
public:
    /**
     * @brief Get Arabic translation for a given English text
     * @param english English text
     * @return Arabic translation or original text if not found
     */
    static QString translate(const QString &english);
    
    /**
     * @brief Initialize the translation hash
     */
    static void initialize();
    
private:
    static QHash<QString, QString> m_translations;
    static bool m_initialized;
};

#endif // ARABICTRANSLATIONS_H
