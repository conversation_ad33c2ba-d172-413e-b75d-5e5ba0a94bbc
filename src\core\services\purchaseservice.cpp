#include "purchaseservice.h"
#include <QDateTime>

PurchaseService::PurchaseService(QObject *parent)
    : QObject(parent),
      m_inventoryService(this)
{
    // Initialize database
    m_dbManager.initialize();
}

QList<QSharedPointer<Invoice>> PurchaseService::getAllPurchaseInvoices()
{
    QList<QSharedPointer<Invoice>> invoices;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE type = 'purchase' ORDER BY created_at DESC");
    
    if (query.exec()) {
        while (query.next()) {
            QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
            loadInvoiceItems(invoice);
            invoices.append(invoice);
        }
    } else {
        qDebug() << "Failed to get purchase invoices:" << query.lastError().text();
    }
    
    return invoices;
}

QSharedPointer<Invoice> PurchaseService::getPurchaseInvoiceById(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE id = ? AND type = 'purchase'");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
        loadInvoiceItems(invoice);
        return invoice;
    } else {
        qDebug() << "Failed to get purchase invoice by ID:" << query.lastError().text();
        return nullptr;
    }
}

QSharedPointer<Invoice> PurchaseService::getPurchaseInvoiceByNumber(const QString &invoiceNumber)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoices WHERE invoice_number = ? AND type = 'purchase'");
    query.addBindValue(invoiceNumber);
    
    if (query.exec() && query.next()) {
        QSharedPointer<Invoice> invoice = createInvoiceFromQuery(query);
        loadInvoiceItems(invoice);
        return invoice;
    } else {
        qDebug() << "Failed to get purchase invoice by number:" << query.lastError().text();
        return nullptr;
    }
}

bool PurchaseService::createPurchaseInvoice(const QSharedPointer<Invoice> &invoice)
{
    // Start a transaction
    QSqlDatabase db = m_dbManager.database();
    db.transaction();
    
    // Set invoice type to Purchase
    invoice->setType(Invoice::Type::Purchase);
    
    // Generate invoice number if not set
    if (invoice->invoiceNumber().isEmpty()) {
        invoice->setInvoiceNumber(generateInvoiceNumber());
    }
    
    // Insert the invoice
    QSqlQuery query(db);
    query.prepare("INSERT INTO invoices (invoice_number, type, supplier_id, total_amount, "
                  "discount, tax, paid_amount, status, notes) "
                  "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    query.addBindValue(invoice->invoiceNumber());
    query.addBindValue(Invoice::typeToString(invoice->type()));
    query.addBindValue(invoice->supplierId());
    query.addBindValue(invoice->totalAmount());
    query.addBindValue(invoice->discount());
    query.addBindValue(invoice->tax());
    query.addBindValue(invoice->paidAmount());
    query.addBindValue(Invoice::statusToString(invoice->status()));
    query.addBindValue(invoice->notes());
    
    if (!query.exec()) {
        qDebug() << "Failed to create purchase invoice:" << query.lastError().text();
        db.rollback();
        return false;
    }
    
    // Set the ID of the newly added invoice
    invoice->setId(query.lastInsertId().toInt());
    
    // Save invoice items
    if (!saveInvoiceItems(invoice)) {
        db.rollback();
        return false;
    }
    
    // Update inventory quantities
    if (!updateInventory(invoice, true)) {
        db.rollback();
        return false;
    }
    
    // Update supplier balance if not fully paid
    if (invoice->remainingAmount() > 0) {
        if (!updateSupplierBalance(invoice->supplierId(), invoice->remainingAmount())) {
            db.rollback();
            return false;
        }
    }
    
    // Commit the transaction
    return db.commit();
}

bool PurchaseService::updatePurchaseInvoice(const QSharedPointer<Invoice> &invoice)
{
    // TODO: Implement update purchase invoice
    return false;
}

bool PurchaseService::deletePurchaseInvoice(int id)
{
    // TODO: Implement delete purchase invoice
    return false;
}

bool PurchaseService::addPayment(int invoiceId, double amount, const QString &paymentMethod,
                                const QString &reference, const QString &notes)
{
    // TODO: Implement add payment
    return false;
}

QList<QSharedPointer<Supplier>> PurchaseService::getAllSuppliers()
{
    QList<QSharedPointer<Supplier>> suppliers;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM suppliers ORDER BY name");
    
    if (query.exec()) {
        while (query.next()) {
            suppliers.append(createSupplierFromQuery(query));
        }
    } else {
        qDebug() << "Failed to get suppliers:" << query.lastError().text();
    }
    
    return suppliers;
}

QSharedPointer<Supplier> PurchaseService::getSupplierById(int id)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM suppliers WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        return createSupplierFromQuery(query);
    } else {
        qDebug() << "Failed to get supplier by ID:" << query.lastError().text();
        return nullptr;
    }
}

bool PurchaseService::addSupplier(const QSharedPointer<Supplier> &supplier)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("INSERT INTO suppliers (name, phone, email, address, balance) "
                  "VALUES (?, ?, ?, ?, ?)");
    
    query.addBindValue(supplier->name());
    query.addBindValue(supplier->phone());
    query.addBindValue(supplier->email());
    query.addBindValue(supplier->address());
    query.addBindValue(supplier->balance());
    
    if (query.exec()) {
        // Set the ID of the newly added supplier
        supplier->setId(query.lastInsertId().toInt());
        return true;
    } else {
        qDebug() << "Failed to add supplier:" << query.lastError().text();
        return false;
    }
}

bool PurchaseService::updateSupplier(const QSharedPointer<Supplier> &supplier)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE suppliers SET name = ?, phone = ?, email = ?, address = ?, "
                  "balance = ?, updated_at = CURRENT_TIMESTAMP "
                  "WHERE id = ?");
    
    query.addBindValue(supplier->name());
    query.addBindValue(supplier->phone());
    query.addBindValue(supplier->email());
    query.addBindValue(supplier->address());
    query.addBindValue(supplier->balance());
    query.addBindValue(supplier->id());
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update supplier:" << query.lastError().text();
        return false;
    }
}

bool PurchaseService::deleteSupplier(int id)
{
    // Check if supplier has invoices
    QSqlQuery checkQuery(m_dbManager.database());
    checkQuery.prepare("SELECT COUNT(*) FROM invoices WHERE supplier_id = ?");
    checkQuery.addBindValue(id);
    
    if (checkQuery.exec() && checkQuery.next()) {
        int count = checkQuery.value(0).toInt();
        if (count > 0) {
            qDebug() << "Cannot delete supplier with invoices";
            return false;
        }
    } else {
        qDebug() << "Failed to check supplier invoices:" << checkQuery.lastError().text();
        return false;
    }
    
    // Delete the supplier
    QSqlQuery query(m_dbManager.database());
    query.prepare("DELETE FROM suppliers WHERE id = ?");
    query.addBindValue(id);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to delete supplier:" << query.lastError().text();
        return false;
    }
}

QList<QSharedPointer<Supplier>> PurchaseService::searchSuppliers(const QString &searchTerm)
{
    QList<QSharedPointer<Supplier>> suppliers;
    
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM suppliers WHERE name LIKE ? OR phone LIKE ? ORDER BY name");
    query.addBindValue("%" + searchTerm + "%");
    query.addBindValue("%" + searchTerm + "%");
    
    if (query.exec()) {
        while (query.next()) {
            suppliers.append(createSupplierFromQuery(query));
        }
    } else {
        qDebug() << "Failed to search suppliers:" << query.lastError().text();
    }
    
    return suppliers;
}

QString PurchaseService::generateInvoiceNumber()
{
    // Get the current date
    QDate currentDate = QDate::currentDate();
    QString yearMonth = currentDate.toString("yyyyMM");
    
    // Get the last invoice number for this month
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT MAX(invoice_number) FROM invoices WHERE invoice_number LIKE ?");
    query.addBindValue("PUR-" + yearMonth + "-%");
    
    int lastNumber = 0;
    if (query.exec() && query.next()) {
        QString lastInvoiceNumber = query.value(0).toString();
        if (!lastInvoiceNumber.isEmpty()) {
            QStringList parts = lastInvoiceNumber.split("-");
            if (parts.size() == 3) {
                lastNumber = parts[2].toInt();
            }
        }
    }
    
    // Generate the new invoice number
    return QString("PUR-%1-%2").arg(yearMonth).arg(lastNumber + 1, 4, 10, QChar('0'));
}

QSharedPointer<Invoice> PurchaseService::createInvoiceFromQuery(QSqlQuery &query)
{
    QSharedPointer<Invoice> invoice = QSharedPointer<Invoice>(new Invoice());
    
    invoice->setId(query.value("id").toInt());
    invoice->setInvoiceNumber(query.value("invoice_number").toString());
    invoice->setType(Invoice::typeFromString(query.value("type").toString()));
    invoice->setCustomerId(query.value("customer_id").toInt());
    invoice->setSupplierId(query.value("supplier_id").toInt());
    invoice->setTotalAmount(query.value("total_amount").toDouble());
    invoice->setDiscount(query.value("discount").toDouble());
    invoice->setTax(query.value("tax").toDouble());
    invoice->setPaidAmount(query.value("paid_amount").toDouble());
    invoice->setStatus(Invoice::statusFromString(query.value("status").toString()));
    invoice->setNotes(query.value("notes").toString());
    invoice->setCreatedAt(query.value("created_at").toDateTime());
    
    return invoice;
}

QSharedPointer<Supplier> PurchaseService::createSupplierFromQuery(QSqlQuery &query)
{
    QSharedPointer<Supplier> supplier = QSharedPointer<Supplier>(new Supplier());
    
    supplier->setId(query.value("id").toInt());
    supplier->setName(query.value("name").toString());
    supplier->setPhone(query.value("phone").toString());
    supplier->setEmail(query.value("email").toString());
    supplier->setAddress(query.value("address").toString());
    supplier->setBalance(query.value("balance").toDouble());
    supplier->setCreatedAt(query.value("created_at").toDateTime());
    supplier->setUpdatedAt(query.value("updated_at").toDateTime());
    
    return supplier;
}

bool PurchaseService::loadInvoiceItems(const QSharedPointer<Invoice> &invoice)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    query.addBindValue(invoice->id());
    
    if (query.exec()) {
        QList<QSharedPointer<InvoiceItem>> items;
        while (query.next()) {
            QSharedPointer<InvoiceItem> item = QSharedPointer<InvoiceItem>(new InvoiceItem());
            
            item->setId(query.value("id").toInt());
            item->setInvoiceId(query.value("invoice_id").toInt());
            item->setProductId(query.value("product_id").toInt());
            item->setQuantity(query.value("quantity").toInt());
            item->setUnitPrice(query.value("unit_price").toDouble());
            item->setTotalPrice(query.value("total_price").toDouble());
            item->setCreatedAt(query.value("created_at").toDateTime());
            
            items.append(item);
        }
        invoice->setItems(items);
        return true;
    } else {
        qDebug() << "Failed to load invoice items:" << query.lastError().text();
        return false;
    }
}

bool PurchaseService::saveInvoiceItems(const QSharedPointer<Invoice> &invoice)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price) "
                  "VALUES (?, ?, ?, ?, ?)");
    
    for (const QSharedPointer<InvoiceItem> &item : invoice->items()) {
        query.addBindValue(invoice->id());
        query.addBindValue(item->productId());
        query.addBindValue(item->quantity());
        query.addBindValue(item->unitPrice());
        query.addBindValue(item->totalPrice());
        
        if (!query.exec()) {
            qDebug() << "Failed to save invoice item:" << query.lastError().text();
            return false;
        }
        
        // Set the ID of the newly added item
        item->setId(query.lastInsertId().toInt());
    }
    
    return true;
}

bool PurchaseService::updateSupplierBalance(int supplierId, double amount)
{
    QSqlQuery query(m_dbManager.database());
    query.prepare("UPDATE suppliers SET balance = balance + ?, updated_at = CURRENT_TIMESTAMP "
                  "WHERE id = ?");
    query.addBindValue(amount);
    query.addBindValue(supplierId);
    
    if (query.exec()) {
        return true;
    } else {
        qDebug() << "Failed to update supplier balance:" << query.lastError().text();
        return false;
    }
}

bool PurchaseService::updateInventory(const QSharedPointer<Invoice> &invoice, bool isCreating)
{
    for (const QSharedPointer<InvoiceItem> &item : invoice->items()) {
        QSharedPointer<Product> product = m_inventoryService.getProductById(item->productId());
        if (!product) {
            qDebug() << "Product not found:" << item->productId();
            return false;
        }
        
        int newQuantity;
        if (isCreating) {
            // Increase quantity for purchases
            newQuantity = product->quantity() + item->quantity();
        } else {
            // Decrease quantity when deleting purchases
            newQuantity = product->quantity() - item->quantity();
        }
        
        if (!m_inventoryService.updateProductQuantity(product->id(), newQuantity)) {
            return false;
        }
    }
    
    return true;
}
