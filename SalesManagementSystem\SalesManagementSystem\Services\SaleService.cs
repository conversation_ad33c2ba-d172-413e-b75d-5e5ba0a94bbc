using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SaleService
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;

        public SaleService(DatabaseService dbService, ProductService productService, CustomerService customerService)
        {
            _dbService = dbService;
            _productService = productService;
            _customerService = customerService;
        }

        public async Task<IEnumerable<Sale>> GetAllSalesAsync()
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                ORDER BY s.Date DESC";

            var sales = await _dbService.QueryAsync<Sale>(sql);
            foreach (var sale in sales)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sales;
        }

        public async Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                WHERE s.Date BETWEEN @StartDate AND @EndDate
                ORDER BY s.Date DESC";

            var sales = await _dbService.QueryAsync<Sale>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            foreach (var sale in sales)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sales;
        }

        public async Task<IEnumerable<Sale>> GetSalesByCustomerAsync(int customerId)
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                WHERE s.CustomerId = @CustomerId
                ORDER BY s.Date DESC";

            var sales = await _dbService.QueryAsync<Sale>(sql, new { CustomerId = customerId });

            foreach (var sale in sales)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sales;
        }

        public async Task<IEnumerable<Sale>> SearchSalesAsync(string searchTerm)
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                WHERE s.InvoiceNumber LIKE @SearchTerm OR c.Name LIKE @SearchTerm
                ORDER BY s.Date DESC";

            var sales = await _dbService.QueryAsync<Sale>(sql, new { SearchTerm = $"%{searchTerm}%" });

            foreach (var sale in sales)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sales;
        }

        public async Task<Sale> GetSaleByIdAsync(int id)
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                WHERE s.Id = @Id";

            var sale = await _dbService.QuerySingleOrDefaultAsync<Sale>(sql, new { Id = id });

            if (sale != null)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sale;
        }

        public async Task<Sale> GetSaleByInvoiceNumberAsync(string invoiceNumber)
        {
            const string sql = @"
                SELECT s.*, c.Name as CustomerName 
                FROM Sales s
                LEFT JOIN Customers c ON s.CustomerId = c.Id
                WHERE s.InvoiceNumber = @InvoiceNumber";

            var sale = await _dbService.QuerySingleOrDefaultAsync<Sale>(sql, new { InvoiceNumber = invoiceNumber });

            if (sale != null)
            {
                sale.Items = new System.Collections.ObjectModel.ObservableCollection<SaleItem>(
                    await GetSaleItemsAsync(sale.Id));
            }

            return sale;
        }

        public async Task<IEnumerable<SaleItem>> GetSaleItemsAsync(int saleId)
        {
            const string sql = @"
                SELECT si.*, p.Name as ProductName, p.Code as ProductCode
                FROM SaleItems si
                INNER JOIN Products p ON si.ProductId = p.Id
                WHERE si.SaleId = @SaleId";

            return await _dbService.QueryAsync<SaleItem>(sql, new { SaleId = saleId });
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            // Format: INV-YYYYMMDD-XXXX where XXXX is a sequential number
            string datePrefix = DateTime.Now.ToString("yyyyMMdd");
            string sql = $"SELECT COUNT(*) FROM Sales WHERE InvoiceNumber LIKE 'INV-{datePrefix}-%'";
            int count = await _dbService.QuerySingleOrDefaultAsync<int>(sql);
            return $"INV-{datePrefix}-{(count + 1).ToString("D4")}";
        }

        public async Task<Sale> AddSaleAsync(Sale sale)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Generate invoice number if not provided
                    if (string.IsNullOrEmpty(sale.InvoiceNumber))
                    {
                        sale.InvoiceNumber = await GenerateInvoiceNumberAsync();
                    }

                    // Set timestamps
                    sale.CreatedAt = DateTime.Now;

                    // Insert sale
                    var insertedSale = await _dbService.InsertAsync<Sale>("Sales", sale);

                    // Insert sale items
                    foreach (var item in sale.Items)
                    {
                        item.SaleId = insertedSale.Id;
                        await _dbService.InsertAsync<SaleItem>("SaleItems", item);

                        // Update product stock
                        await _productService.UpdateProductStockAsync(item.ProductId, -item.Quantity);
                    }

                    // Update customer balance if payment status is not "Paid"
                    if (sale.CustomerId.HasValue && sale.PaymentStatus != "Paid")
                    {
                        await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, sale.Total);
                    }

                    transaction.Complete();
                    return insertedSale;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> UpdateSaleAsync(Sale sale)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get original sale to calculate differences
                    var originalSale = await GetSaleByIdAsync(sale.Id);
                    if (originalSale == null)
                    {
                        return false;
                    }

                    // Set update timestamp
                    sale.UpdatedAt = DateTime.Now;

                    // Update sale
                    await _dbService.UpdateAsync("Sales", sale);

                    // Delete existing sale items
                    await _dbService.ExecuteAsync("DELETE FROM SaleItems WHERE SaleId = @SaleId", new { SaleId = sale.Id });

                    // Restore original product quantities
                    foreach (var item in originalSale.Items)
                    {
                        await _productService.UpdateProductStockAsync(item.ProductId, item.Quantity);
                    }

                    // Insert new sale items and update product quantities
                    foreach (var item in sale.Items)
                    {
                        item.SaleId = sale.Id;
                        await _dbService.InsertAsync<SaleItem>("SaleItems", item);
                        await _productService.UpdateProductStockAsync(item.ProductId, -item.Quantity);
                    }

                    // Update customer balance if payment status changed
                    if (sale.CustomerId.HasValue && originalSale.PaymentStatus != sale.PaymentStatus)
                    {
                        if (originalSale.PaymentStatus != "Paid" && sale.PaymentStatus == "Paid")
                        {
                            // If changed from unpaid to paid, reduce customer balance
                            await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, -originalSale.Total);
                        }
                        else if (originalSale.PaymentStatus == "Paid" && sale.PaymentStatus != "Paid")
                        {
                            // If changed from paid to unpaid, increase customer balance
                            await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, sale.Total);
                        }
                    }

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> DeleteSaleAsync(int id)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get sale to restore product quantities and update customer balance
                    var sale = await GetSaleByIdAsync(id);
                    if (sale == null)
                    {
                        return false;
                    }

                    // Restore product quantities
                    foreach (var item in sale.Items)
                    {
                        await _productService.UpdateProductStockAsync(item.ProductId, item.Quantity);
                    }

                    // Update customer balance if payment status is not "Paid"
                    if (sale.CustomerId.HasValue && sale.PaymentStatus != "Paid")
                    {
                        await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, -sale.Total);
                    }

                    // Delete sale items (will be cascaded by foreign key constraint)
                    await _dbService.ExecuteAsync("DELETE FROM SaleItems WHERE SaleId = @SaleId", new { SaleId = id });

                    // Delete sale
                    await _dbService.DeleteAsync("Sales", id);

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<bool> UpdateSalePaymentStatusAsync(int saleId, string paymentStatus)
        {
            using (var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Get original sale
                    var sale = await GetSaleByIdAsync(saleId);
                    if (sale == null)
                    {
                        return false;
                    }

                    // Update payment status
                    const string sql = @"
                        UPDATE Sales 
                        SET PaymentStatus = @PaymentStatus, UpdatedAt = @UpdatedAt 
                        WHERE Id = @SaleId";

                    await _dbService.ExecuteAsync(sql, new
                    {
                        SaleId = saleId,
                        PaymentStatus = paymentStatus,
                        UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                    });

                    // Update customer balance if payment status changed
                    if (sale.CustomerId.HasValue)
                    {
                        if (sale.PaymentStatus != "Paid" && paymentStatus == "Paid")
                        {
                            // If changed from unpaid to paid, reduce customer balance
                            await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, -sale.Total);
                        }
                        else if (sale.PaymentStatus == "Paid" && paymentStatus != "Paid")
                        {
                            // If changed from paid to unpaid, increase customer balance
                            await _customerService.UpdateCustomerBalanceAsync(sale.CustomerId.Value, sale.Total);
                        }
                    }

                    transaction.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // Transaction will automatically be rolled back
                    throw;
                }
            }
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "WHERE Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $"SELECT SUM(Total) FROM Sales {dateFilter}";

            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql, new
            {
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });

            return result ?? 0;
        }

        public async Task<IEnumerable<SaleSummaryByMonth>> GetSaleSummaryByMonthAsync(int year)
        {
            const string sql = @"
                SELECT strftime('%m', Date) as Month, SUM(Total) as TotalAmount, COUNT(*) as SaleCount
                FROM Sales
                WHERE strftime('%Y', Date) = @Year
                GROUP BY strftime('%m', Date)
                ORDER BY Month";

            return await _dbService.QueryAsync<SaleSummaryByMonth>(sql, new { Year = year.ToString() });
        }

        public async Task<IEnumerable<SaleSummaryByProduct>> GetSaleSummaryByProductAsync(DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT p.Id, p.Name, p.Code, SUM(si.Quantity) as TotalQuantity, SUM(si.Total) as TotalAmount
                FROM SaleItems si
                INNER JOIN Products p ON si.ProductId = p.Id
                INNER JOIN Sales s ON si.SaleId = s.Id
                WHERE s.Date BETWEEN @StartDate AND @EndDate
                GROUP BY p.Id, p.Name, p.Code
                ORDER BY TotalAmount DESC";

            return await _dbService.QueryAsync<SaleSummaryByProduct>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });
        }
    }

    public class SaleSummaryByMonth
    {
        public string Month { get; set; }
        public decimal TotalAmount { get; set; }
        public int SaleCount { get; set; }

        public string MonthName => int.TryParse(Month, out int monthNumber) 
            ? new DateTime(2000, monthNumber, 1).ToString("MMMM") 
            : Month;
    }

    public class SaleSummaryByProduct
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalAmount { get; set; }
    }
}